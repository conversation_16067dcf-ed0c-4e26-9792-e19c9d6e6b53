#!/usr/bin/env node
/* eslint-disable eslint-comments/no-unlimited-disable */

/* eslint-disable */
'use strict';

import path from 'node:path';
import fs from 'node:fs';
import axios from 'axios';
let [rewardUrl, developer, rewardSheetIndex, msgSheetIndex, luckyDrawStartSheetIndexStr] =
    process.argv.slice(2);
/**
 * https://q9jvw0u5f5.feishu.cn/sheets/shtcno1KoZj7by2nrjDTtR5QGQi // 资源url
 * shtcno1KoZj7by2nrjDTtR5QGQi // excelId
 */
rewardUrl = rewardUrl || 'https://q9jvw0u5f5.feishu.cn/sheets/KSoXs53ESh8lRpt849ec9ARGn3d'; // 资源url
developer = developer || 2; // 1-后端使用 2-前端使用
rewardSheetIndex = +rewardSheetIndex || 0; // 默认第1个sheetId为资源奖励
msgSheetIndex = +msgSheetIndex || 1; // 默认第2个sheetId为消息配置
let spreadsheetId = getExcelId(); // excelId
const rootDir = process.cwd();
const imageFolder = path.join(rootDir, '/src/assets/img/rewards/'); // 前端图片文件夹
const rewardFolder = path.join(rootDir, '/src/config/'); // 前端配置文件夹
const imageType = '.png'; // 前端图片类型
let token;
let spreadSheetInfo = {};

// 礼物类型
const REWARD_TYPE = {
    PACKAGE: {
        type: 'package',
        name: '包裹',
        unit: '个',
    },
    GIFT: {
        type: 'gift',
        name: '礼物',
        unit: '个',
    },
    MEDAL: {
        type: 'medal',
        name: '勋章',
        unit: '天',
    },
    HEADWEAR: {
        type: 'headwear',
        name: '麦位框',
        unit: '天',
    },
    MOUNT: {
        type: 'mount',
        name: '坐骑',
        unit: '天',
    },
    ENTER_ROOM_EFFECT: {
        type: 'mount',
        name: '进房板',
        unit: '天',
    },
    FLUTTER: {
        type: 'flutter',
        name: '主页飘',
        unit: '天',
    },
    BG: {
        type: 'bg',
        name: '直播房间背景',
        unit: '天',
    },
    UGC_BG: {
        type: 'ugc_bg',
        name: '个人房间背景',
        unit: '天',
    },
    FANS_PLATE: {
        type: 'fans_plate',
        name: '粉丝团铭牌',
        unit: '天',
    },
    USER_PLATE: {
        type: 'user_plate',
        name: '个人铭牌',
        unit: '天',
    },
    OFFICIAL_CERT: {
        type: 'official_cert',
        name: '大V认证',
        unit: '天',
    },
    PRESENT_WALL_EFFECT: {
        type: 'present_wall_effect',
        name: '礼物墙光效',
        unit: '天',
    },
    PRESENT_PRIVILEGE: {
        type: 'present_privilege',
        name: '礼物赠送权',
        unit: '天',
    },
    LIVE_PREPARE_CHANNEL: {
        type: 'live_prepare_channel',
        name: '特殊标签',
        unit: '天',
    },
    PRESENT_EFFECT: {
        type: 'present_effect',
        name: '送礼特效',
        unit: '次',
    },
    PALACE_GRADE: {
        type: 'palace_grade',
        name: '殿堂积分',
        unit: '分',
    },
    SUPER_PLAYER_EXPERIENCE: {
        type: 'super_player_experience',
        name: '会员体验卡',
        unit: '天',
    },
    MEMBER_GROWTH_VALUE: {
        type: 'member_growth_value',
        name: '会员活动成长值',
        unit: '',
    },
    ANCHOR_IDENTITY: {
        type: 'anchor_identity',
        name: '身份认证',
        unit: '天',
    },
    VIRTUAL_IMAGE: {
        type: 'virtual_image',
        name: '虚拟形象',
        unit: '天',
    },
    BREAKING_NEWS: {
        type: 'breaking_news',
        name: '全服公告',
        unit: '条',
    },
    POP_WINDOW: {
        type: 'pop_window',
        name: '房间弹窗',
        unit: '次',
    },
    FLOW_CARD: {
        type: 'flow_card',
        name: '流量卡',
        unit: '张',
    },
    RED_DIAMOND: {
        type: 'red_diamond',
        name: '红钻',
        unit: '个',
    },
    TBean: {
        type: 't_bean',
        name: 'T豆',
        unit: '个',
    },
    EXPERIENCE: {
        type: 'experience',
        name: '经验值',
        unit: '个',
    },
    ENTITY: {
        type: 'entity',
        name: '实体礼物',
        unit: '个',
    },
    INTIMACY_MULTIPLE: {
        type: 'intimacy_multiple',
        name: '亲密值加倍',
        unit: '天',
    },
    BASE_FLOW_CHANNEL: {
        type: 'base_flow_channel',
        name: '基础流量',
        unit: '天',
    },
    OTHER: {
        type: 'other',
        name: '其它',
        unit: '天',
    },
};

const rewardSheetFieldMaps = {
    resourceId: '资源ID(唯一)',
    imageId: '图片资源ID',
    module: '应用模块',
    rewardType: '奖励类型',
    specialType: '特殊类型',
    price: '价值',
    testingName: '云测名称',
    testingId: '云测ID',
    prodName: '正式名称',
    prodId: '正式ID',
    prodImage: '正式设计资源图',
    prodDynamicUrl: '正式设计动效链接',
    prodImageUrl: '正式设计资源图链接',
    diyRewardType: '自定义奖励类型显示',
};

const msgSheetFieldMaps = {
    resourceId: '资源ID(唯一)',
    module: '应用模块',
    scene: '推送场景',
    pushType: '推送方式',
    content: '消息内容',
    jumpTab: '点击跳转定位',
    jumpUrl: '跳转链接',
    remark1: '备注1',
    imageUrl: '图文推送图片链接',
    title: '图文推送标题',
};

function getExcelId() {
    try {
        const list = rewardUrl.split('/');
        let excelId = list[list.length - 1];
        if (excelId.includes('?')) {
            excelId = excelId.split('?')[0];
        }
        return excelId;
    } catch (error) {
        throw 'ERROR: 请检查资源url是否正确！';
    }
}

const REWARD_C2E_MAP = (function getC2E() {
    const rMap = {};
    for (const key in REWARD_TYPE) {
        const item = REWARD_TYPE[key];
        rMap[item.name] = item.type;
    }
    return rMap;
})();
const rewardTypes = Object.keys(REWARD_C2E_MAP);

async function getToken() {
    const tokenInfo = await axios({
        url: 'https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal',
        method: 'post',
        data: {
            app_id: 'cli_a3e9cd7ec5fd1013',
            app_secret: 'QovCecIAGCNqaa7rIcvLHeYzPGrOBUdg',
        },
    });
    const token = 'Bearer ' + tokenInfo.data.tenant_access_token;
    return token;
}

async function getWikiToken() {
    token = await getToken();
    try {
        const sheetInfo = await axios({
            url: `https://open.feishu.cn/open-apis/wiki/v2/spaces/get_node`,
            method: 'get',
            headers: {
                Authorization: token,
            },
            params: {
                token: spreadsheetId,
            },
        });
        return sheetInfo.data.data.node.obj_token;
    } catch (error) {
        console.error(` getWikiToken fail: ${error} `);
    }
}

async function getSpreadSheetInfo() {
    token = await getToken();
    spreadSheetInfo = await axios({
        url: `https://open.feishu.cn/open-apis/sheets/v3/spreadsheets/${spreadsheetId}/sheets/query`,
        method: 'get',
        headers: {
            Authorization: token,
        },
    });
    const sheetTable = spreadSheetInfo.data.data.sheets.map((sheet) => [
        sheet.title,
        sheet.sheet_id,
    ]);
    // 添加 console.table 特殊的标识符, 便于小T识别过滤
    console.log('table output >>>');
    console.table(sheetTable);
    console.log('table output <<<');
}

async function getSheet(sheetIndex) {
    const sheetIdLIst = spreadSheetInfo.data.data.sheets.map((sheet) => sheet.sheet_id);
    return sheetIdLIst[sheetIndex];
}

async function getSheetData(sheetIndex) {
    try {
        const sheetId = await getSheet(sheetIndex);
        if (!sheetId) {
            return [];
        }
        const res = await axios({
            url: `https://open.feishu.cn/open-apis/sheets/v2/spreadsheets/${spreadsheetId}/values_batch_get`,
            method: 'get',
            headers: {
                Authorization: token,
                'Content-Type': 'application/json; charset=utf-8',
            },
            params: {
                ranges: sheetId,
                valueRenderOption: 'FormattedValue',
            },
        });
        const datas = res.data.data.valueRanges[0].values;
        return datas;
    } catch (err) {
        console.error(` getSheetData fail: ${err} `);
    }
}

async function getResourceList(sheetIndex, sheetFieldMaps, type) {
    const values = await getSheetData(sheetIndex);
    if (!values || !values.length) {
        return [];
    }
    const needSerializeFieldsIndex = type === 'config' ? 0 : 1;
    const valueIndex = type === 'config' ? 1 : 2;
    const needSerializeFieldsMap = {
        0: '资源ID(唯一)',
    };
    const sheetFieldNames = Object.values(sheetFieldMaps);
    values[needSerializeFieldsIndex].forEach((item, index) => {
        if (index > 0) {
            if (sheetFieldNames.includes(item)) {
                needSerializeFieldsMap[index] = item;
            } else {
                needSerializeFieldsMap[index] = item;
            }
        }
    });
    const reversedSheetFieldMaps = {};
    for (const [key, value] of Object.entries(sheetFieldMaps)) {
        reversedSheetFieldMaps[value] = key;
    }
    const list = [];
    for (let index = valueIndex; index < values.length; index++) {
        const datas = values[index];
        if (datas && datas.length && !datas[0]) {
            continue;
        }
        const dataObj = {};
        for (let index = 0; index < datas.length; index++) {
            if (!needSerializeFieldsMap[index] || !datas[index]) {
                continue;
            }
            const dataField =
                reversedSheetFieldMaps[needSerializeFieldsMap[index]] ||
                needSerializeFieldsMap[index];
            if (typeof datas[index] === 'string') {
                let item = datas[index].replace(/\s/g, '');
                item =
                    type === 'msg'
                        ? item.includes('\\n')
                            ? item.replace(/\\n/g, '\n')
                            : item
                        : item;
                dataObj[dataField] = item;
                if (dataField === 'specialType' && item.length) {
                    dataObj[dataField] = item.split(',');
                }
            } else {
                dataObj[dataField] = datas[index];
            }
        }
        if (Object.keys(dataObj).length) {
            list.push(dataObj);
        }
    }
    if (list.length !== new Set(list.map((l) => l.resourceId)).size) {
        const repeatResourceIds = list
            .filter(
                (item, index, self) =>
                    self.findIndex((t) => t.resourceId === item.resourceId) !== index,
            )
            .map((item) => item.resourceId);
        throw `ERROR: 请检查资源ID是否唯一！重复的资源ID: 【${repeatResourceIds.join(', ')}】`;
    }
    return list;
}

const typeStr = {
    男: 'sex: 1',
    女: 'sex: 0',
    CP: 'cp: 1',
    融合: 'fusion: 1',
    自定义: 'custom_text: "自定义文案"',
};
const canFormatSpecialType = Object.keys(typeStr);
function formatSpecialType(specialType) {
    if (!Array.isArray(specialType)) {
        return '';
    }
    const needFormatSpecialType = specialType.filter((item) => canFormatSpecialType.includes(item));
    if (needFormatSpecialType.length) {
        return needFormatSpecialType.map((item) => typeStr[item]).join(',');
    }
    return '';
}

async function downloadsImageResources(rewardList) {
    if (!fs.existsSync(imageFolder)) {
        fs.mkdirSync(
            imageFolder,
            {
                recursive: true,
            },
            (error) => {
                if (error) throw error;
            },
        );
    }
    try {
        for (const reward of rewardList) {
            if (reward.prodImage && reward.prodImage.fileToken) {
                const res = await axios({
                    url: `https://open.feishu.cn/open-apis/drive/v1/medias/${reward.prodImage.fileToken}/download`,
                    method: 'get',
                    headers: {
                        Authorization: token,
                    },
                    responseType: 'arraybuffer',
                });
                const imageId = reward.imageId || reward.resourceId;
                fs.writeFile(
                    `${imageFolder}${imageId}${imageType}`,
                    Buffer.from(res.data),
                    (error) => {
                        if (error) {
                            console.error(error);
                            return;
                        }
                        console.log(` ${imageId}${imageType} 已被保存 `);
                    },
                );
            }
        }
        console.log(` downloadsImageResourcesEnd `);
    } catch (error) {
        console.error(` downloadsImageResources fail: ${error} `);
    }
}

async function generateRewardConfigFrontEnd() {
    const rewardList = await getResourceList(rewardSheetIndex, rewardSheetFieldMaps);
    const REWARD_CONFIG_LIST = [];
    const REWARD_CONFIG_MAP = {};
    let module;
    for (const reward of rewardList) {
        module = reward.module || module;
        let type = REWARD_TYPE.OTHER.type,
            unit = REWARD_TYPE.OTHER.unit,
            mark = reward.rewardType;
        if (rewardTypes.includes(reward.rewardType)) {
            type = REWARD_C2E_MAP[reward.rewardType];
            mark = REWARD_TYPE[REWARD_C2E_MAP[reward.rewardType].toUpperCase()].name;
            unit = REWARD_TYPE[REWARD_C2E_MAP[reward.rewardType].toUpperCase()].unit;
        }
        let item = {
            resource_id: reward.resourceId,
            name: reward.prodName || reward.testingName,
            type,
            mark,
            module,
            unit,
            price: reward.price,
            special_type: reward.specialType ? reward.specialType[0] : '',
            image: reward.imageId
                ? `${reward.imageId}${imageType}`
                : `${reward.resourceId}${imageType}`,
            image_url:
                reward.prodImageUrl &&
                reward.prodImageUrl.length &&
                reward.prodImageUrl[0] &&
                reward.prodImageUrl[0].link,
            dynamic_url:
                reward.prodDynamicUrl &&
                reward.prodDynamicUrl.length &&
                reward.prodDynamicUrl[0] &&
                reward.prodDynamicUrl[0].link,
            diy_reward_type: reward.diyRewardType,
        };
        const otherFields = Object.keys(reward).filter(
            (key) => !Object.values(rewardSheetFieldMaps).includes(key) && key.includes('研发'),
        );
        otherFields.forEach((field) => {
            item[field] = reward[field];
        });
        REWARD_CONFIG_LIST.push(item);
        REWARD_CONFIG_MAP[reward.resourceId] = item;
    }
    const serializedRewardListString = JSON.stringify(REWARD_CONFIG_LIST, null, 4);
    const serializedRewardMapString = JSON.stringify(REWARD_CONFIG_MAP, null, 4);
    fs.writeFile(
        `${rewardFolder}reward.js`,
        'export const rewardList = ' +
            serializedRewardListString +
            '\n\n' +
            'export const rewardMap = ' +
            serializedRewardMapString +
            '\n\n',
        (error) => {
            if (error) throw error;
            console.log('REWARD_CONFIG has been written to reward.js');
        },
    );
    downloadsImageResources(rewardList);
}

async function generateRewardConfig() {
    // 兼容wiki和excel两种资源
    if (rewardUrl.includes('/wiki/')) {
        spreadsheetId = await getWikiToken();
    }
    await getSpreadSheetInfo();
    await generateRewardConfigFrontEnd();
}

generateRewardConfig();
