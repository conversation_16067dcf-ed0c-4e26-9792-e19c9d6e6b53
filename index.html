<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport"
        content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no, minimal-ui, viewport-fit=cover">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="format-detection" content="telephone=no">
    <meta name="msapplication-tap-highlight" content="no">
    <meta name="revised" id="html-version" content="3.0">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Cache-Control" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>加载中...</title>
    <!-- skeleton 骨架图 -->
    <style>
        .__skeleton {
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.2);
        }

        @keyframes var-skeleton-animation {
            from {
                transform: translateX(-100%);
            }

            to {
                transform: translateX(100%);
            }
        }

        .__skeleton:empty::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
            animation: var-skeleton-animation 1s infinite;
            background: linear-gradient(90deg, hsla(0, 0%, 100%, 0), hsla(0, 0%, 100%, 0.4), hsla(0, 0%, 100%, 0));
            transition: 0.25s background-color;
        }

        body,
        html {
            margin: 0;
            padding: 0;
            position: relative;
            height: 100%;
            width: 100%;
            overflow-x: hidden
        }
    </style>
    <!-- 白屏等问题调试用 -->
    <!-- <script src="https://ga-album-cdnqn.52tt.com/web/lib/eruda/3.0.1/eruda.min.js"></script>
    <script>eruda.init();</script> -->
    <!-- <script src="https://ga-album-cdnqn.52tt.com/web/lib/vconsole@3.15.1/dist/vconsole.min.js"></script>
    <script>new VConsole();</script> -->
    <!-- 飞浆 -->
    <script src="/apm-sdk.js" type="text/javascript"></script>
</head>

<body>
    <div id="app" class="__skeleton"></div>
    <script>
        // CSS预加载 polyfill
        !function (t) { "use strict"; t.loadCSS || (t.loadCSS = function () { }); var e = loadCSS.relpreload = {}; if (e.support = function () { var e; try { e = t.document.createElement("link").relList.supports("preload") } catch (t) { e = !1 } return function () { return e } }(), e.bindMediaToggle = function (t) { var e = t.media || "all"; function a() { t.media = e } t.addEventListener ? t.addEventListener("load", a) : t.attachEvent && t.attachEvent("onload", a), setTimeout(function () { t.rel = "stylesheet", t.media = "only x" }), setTimeout(a, 3e3) }, e.poly = function () { if (!e.support()) for (var a = t.document.getElementsByTagName("link"), n = 0; n < a.length; n++) { var o = a[n]; "preload" !== o.rel || "style" !== o.getAttribute("as") || o.getAttribute("data-loadcss") || (o.setAttribute("data-loadcss", !0), e.bindMediaToggle(o)) } }, !e.support()) { e.poly(); var a = t.setInterval(e.poly, 500); t.addEventListener ? t.addEventListener("load", function () { e.poly(), t.clearInterval(a) }) : t.attachEvent && t.attachEvent("onload", function () { e.poly(), t.clearInterval(a) }) } "undefined" != typeof exports ? exports.loadCSS = loadCSS : t.loadCSS = loadCSS }("undefined" != typeof global ? global : this);
    </script>
    <script type="module" src="/src/main.js"></script>
</body>

</html>