import { URL } from 'node:url';

export function unocssScopedFix() {
    return {
        name: 'unocss-scoped-fix',
        enforce: 'pre',
        apply: 'serve',
        handleHotUpdate: ({ server, modules, timestamp }) => {
            if (modules.length > 0) {
                // 手动使模块热更新失效
                const invalidatedModules = new Set();
                // 检测unocss在vue sfc加入的css style标签
                let cssMods = [];

                for (const mod of modules) {
                    const { searchParams } = new URL(mod.url, import.meta.url);
                    if (searchParams.has('lang.css')) {
                        cssMods.push(mod);
                    }
                }

                if (cssMods.length > 0) {
                    cssMods.forEach((cssMod) => {
                        server.moduleGraph.invalidateModule(cssMod, invalidatedModules, timestamp, true);
                    });
                    return modules.filter((mod) => !cssMods.includes(mod));
                }
            }
        },
    };
}
