/**
 * urlConfig.json 配置规则
 * https://gitlab.52tt.com/frontend/web/activity/url-admin/blob/master/README.md
 **/
import { fileURLToPath } from 'node:url';
import fs from 'node:fs';

const Base64 = {
    _keyStr:
        'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=',
    encode: function (e) {
        let t = '';
        let n, r, i, s, o, u, a;
        let f = 0;
        e = this._utf8_encode(e);
        while (f < e.length) {
            n = e.charCodeAt(f++);
            r = e.charCodeAt(f++);
            i = e.charCodeAt(f++);
            s = n >> 2;
            o = ((n & 3) << 4) | (r >> 4);
            u = ((r & 15) << 2) | (i >> 6);
            a = i & 63;
            if (isNaN(r)) {
                u = a = 64;
            } else if (isNaN(i)) {
                a = 64;
            }
            t =
                t +
                this._keyStr.charAt(s) +
                this._keyStr.charAt(o) +
                this._keyStr.charAt(u) +
                this._keyStr.charAt(a);
        }
        return t;
    },
    _utf8_encode: function (e) {
        e = e.replace(/rn/g, 'n');
        let t = '';
        for (let n = 0; n < e.length; n++) {
            let r = e.charCodeAt(n);
            if (r < 128) {
                t += String.fromCharCode(r);
            } else if (r > 127 && r < 2048) {
                t += String.fromCharCode((r >> 6) | 192);
                t += String.fromCharCode((r & 63) | 128);
            } else {
                t += String.fromCharCode((r >> 12) | 224);
                t += String.fromCharCode(((r >> 6) & 63) | 128);
                t += String.fromCharCode((r & 63) | 128);
            }
        }
        return t;
    },
};

export function getUrlAdmin() {
    try {
        const data = JSON.parse(fs.readFileSync('./urlConfig.json', 'utf-8'));
        data.updateTime =
            new Date().toLocaleDateString() +
            ' ' +
            new Date().toLocaleTimeString();
        const code = Base64.encode(JSON.stringify(data));

        const url = `https://appcdn.52tt.com/testing/frontend-web-activity-url-admin/index.html?code=${code}`;
        const maxLength = 63; // 设置显示的最大长度
        // 截取并省略显示
        const displayUrl =
            url.length > maxLength ? url.slice(0, maxLength) + '...' : url;
        // 使用 ANSI 转义序列创建可点击的链接
        return `\u001b]8;;${url}\u0007${displayUrl}\u001b]8;;\u0007`;
    } catch {}
}

function kolorist(start, end) {
    const open = `\x1b[${start}m`;
    const close = `\x1b[${end}m`;
    const regex = new RegExp(`\\x1b\\[${end}m`, 'g');
    return (str) => {
        return open + ('' + str).replace(regex, open) + close;
    };
}

const bold = kolorist(1, 22);
const green = kolorist(32, 39);
const cyan = kolorist(36, 39);
const colorUrl = (url) =>
    cyan(url.replace(/:(\d+)\//, (_, port) => `:${bold(port)}/`));

export function mfv4UrlConfig() {
    return {
        name: 'mfv4-url-config',
        enforce: 'pre',
        apply: 'serve',
        configureServer: (server) => {
            const _printUrls = server.printUrls;
            server.printUrls = () => {
                console.log(
                    `  ${green('\u279C')}  ${bold('Url Config')}: ${green(`活动全链接平台地址为: ${colorUrl(getUrlAdmin())}`)}`,
                );
                _printUrls();
            };
        },
        handleHotUpdate: (ctx) => {
            if (
                ctx.file ===
                fileURLToPath(new URL('../../urlConfig.json', import.meta.url))
            ) {
                console.log(
                    green(
                        `活动配置已更新, 更新时间：${new Date().toLocaleDateString()} ${new Date().toLocaleTimeString()}`,
                    ),
                );
                console.log(
                    green(
                        `活动全链接平台最新地址为: ${colorUrl(getUrlAdmin())}`,
                    ),
                );
            }
        },
    };
}
