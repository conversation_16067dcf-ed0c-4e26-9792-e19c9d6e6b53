image: cr.ttyuyin.com/frontend/registry/docker-service/activity-h5-images:node18

stages:
    - build
    - deploy
    - cdn

#变量
variables:
    TAOBAO_NPM_REGISTRY: https://registry.npm.taobao.org
    TT_NPM_REGISTRY: https://npm.52tt.com/
    TT_NPM_REGISTRY_LOGIN: https://npm.52tt.com
    CDN_PATH: /data/web/cdn/web/$CI_PROJECT_PATH_SLUG
    #下面的CDN_RELEASE_FOLDER_PATH**都是正式环境cdn要换新的域名
    # cdn - TT
    CDN_RELEASE_FOLDER_PATH: https://appcdn.52tt.com/web/$CI_PROJECT_PATH_SLUG/
    CDN_RELEASE_FOLDER_PATH_SHARE: https://cdn.i52tt.com/web/$CI_PROJECT_PATH_SLUG/
    # cdn - 欢游
    CDN_RELEASE_FOLDER_PATH_HY_IOS: https://appcdn.tses.net/web/$CI_PROJECT_PATH_SLUG/
    CDN_RELEASE_FOLDER_PATH_HY_IOS_SHARE: https://cdn.tses.net/web/$CI_PROJECT_PATH_SLUG/
    CDN_RELEASE_FOLDER_PATH_HY_ANDROID: https://zy-appcdn.rzhushou.com/web/$CI_PROJECT_PATH_SLUG/
    CDN_RELEASE_FOLDER_PATH_HY_ANDROID_SHARE: https://zy-cdn.rzhushou.com/web/$CI_PROJECT_PATH_SLUG/
    # cdn - 麦可
    CDN_RELEASE_FOLDER_PATH_MK_IOS: https://appcdn.tingyou.fun/web/$CI_PROJECT_PATH_SLUG/
    CDN_RELEASE_FOLDER_PATH_MK_IOS_SHARE: https://cdn.tingyou.fun/web/$CI_PROJECT_PATH_SLUG/
    CDN_RELEASE_FOLDER_PATH_MK_ANDROID: https://appcdn.yuyue27.com/web/$CI_PROJECT_PATH_SLUG/
    CDN_RELEASE_FOLDER_PATH_MK_ANDROID_SHARE: https://cdn.yuyue27.com/web/$CI_PROJECT_PATH_SLUG/
    PRODUCTION_PATH: /data/web/app/project/$CI_PROJECT_PATH_SLUG
    GRAY_SCALE_TEST_PATH: /data/web/app/gray/$CI_PROJECT_PATH_SLUG
    INTERNAL_TEST_PATH: /data/web/app/internal/$CI_PROJECT_PATH_SLUG
    CLOUD_TEST_PATH: /data/web/app/testing/$CI_PROJECT_PATH_SLUG
    BI_REPORT_URL: http://miku.ttyuyin.com:8090/pipeline/pipeline/receive_pipeline_result/
    CACHE_KEY: cachekey-active

#script执行前，设置SSH
before_script:
    # - npm-cli-login -r $TT_NPM_REGISTRY_LOGIN -u gitlab-ci-token -p $CI_JOB_TOKEN -e <EMAIL>
    # - npm config set registry $TAOBAO_NPM_REGISTRY
    - echo $'registry=https://npm.52tt.com/\n//npm.52tt.com/:_authToken='$NPM_AUTH_TOKEN >> .npmrc
    - npm config set @tt:registry $TT_NPM_REGISTRY
    - eval $(ssh-agent -s)
    - mkdir -p ~/.ssh
    # ttweb ssh key
    - echo "$APPWEB_SSH_PRIVATE_KEY" > ~/.ssh/appweb_rsa
    - chmod 600 ~/.ssh/appweb_rsa
    - ssh-add ~/.ssh/appweb_rsa
    # gitlab.52tt.com ssh key
    - echo "$GITLAB_SSH_PRIVATE_KEY" > ~/.ssh/gitlab_rsa
    - chmod 600 ~/.ssh/gitlab_rsa
    - ssh-add ~/.ssh/gitlab_rsa
    - echo -e "Host *\\n\\tStrictHostKeyChecking no\\n\\n" > ~/.ssh/config

after_script:
    # build失败上报
    - |
        if [ "$CI_JOB_STATUS" == "failed" ]; then
          echo "JOB failed.";
        elif [ "$CI_JOB_STATUS" == "success" ]; then
          echo "JOB success.";
        else
          echo "JOB didn't finished.";
        fi

# 构建
build:
    stage: build
    # 缓存 node module
    cache:
        key: $CACHE_KEY
        paths:
            - node_modules/
            - dist/
            - imgCompression/
    script:
        - corepack enable pnpm
        - pnpm install
        - |
            if [ "$CI_COMMIT_BRANCH" == "main" ]; then
                npm run build;
            else
                npm run build-test;
            fi
        - export PIPELINE_END_TIME=$(date +%s)
        - export PIPELINE_SPENT_TIME=$((PIPELINE_END_TIME - START_TIME))
        - 'echo "Pipeline duration: ${PIPELINE_SPENT_TIME} seconds"'
    tags:
        - gitlab

#云测试环境
deploy-to-testing:
    stage: deploy
    cache:
        key: $CACHE_KEY
        paths:
            - dist/
    script:
        #建立项目文件夹，并上传文件
        - >
            for server in $APPWEB_SERVER_GROUP; do
              ssh -p 56000 $server rm -rf $CLOUD_TEST_PATH
              ssh -p 56000 $server mkdir -p $CLOUD_TEST_PATH
              rsync -avz -e 'ssh -p 56000' ./dist/ $server:$CLOUD_TEST_PATH/
            done
    environment:
        name: cloud-test
        url: https://app.i52tt.com/testing/$CI_PROJECT_PATH_SLUG/index.html

    only:
        - dev
    tags:
        - gitlab

#灰度测试环境
deploy-to-gray:
    stage: deploy
    cache:
        key: $CACHE_KEY
        paths:
            - dist/
    script:
        #建立项目文件夹，并上传文件
        - >
            for server in $APPWEB_SERVER_GROUP; do
              ssh -p 56000 $server rm -rf $GRAY_SCALE_TEST_PATH
              ssh -p 56000 $server mkdir -p $GRAY_SCALE_TEST_PATH
              rsync -avz -e 'ssh -p 56000' ./dist/ $server:$GRAY_SCALE_TEST_PATH/
            done
    environment:
        name: gray-scale-test
        url: https://app.i52tt.com/gray/$CI_PROJECT_PATH_SLUG/index.html

    only:
        - dev
    tags:
        - gitlab

    #生产环境
deploy-all-to-production:
    stage: deploy
    cache:
        key: $CACHE_KEY
        paths:
            - dist/
    script:
        #建立生产环境项目文件夹并上传
        - >
            for server in $APPWEB_SERVER_GROUP; do
              ssh -p 56000 $server rm -rf $PRODUCTION_PATH
              ssh -p 56000 $server mkdir -p $PRODUCTION_PATH
              rsync -avz -e 'ssh -p 56000' ./dist/ $server:$PRODUCTION_PATH/
            done
        #建立CDN项目文件件并上传
        - >
            for server in $APPWEB_SERVER_GROUP; do
              ssh -p 56000 $server rm -rf $CDN_PATH
              ssh -p 56000 $server mkdir -p $CDN_PATH
              rsync -avz -e 'ssh -p 56000' ./dist/ $server:$CDN_PATH/
            done
    environment:
        name: production
        url: https://appcdn.52tt.com/web/$CI_PROJECT_PATH_SLUG/index.html


    when: manual
    only:
        - main
    tags:
        - gitlab

    #生产环境
deploy-increment-to-production:
    stage: deploy
    cache:
        key: $CACHE_KEY
        paths:
            - dist/
    script:
        # 1. 为 production 环境部署非 HTML 文件
        - >
            for server in $APPWEB_SERVER_GROUP; do
              # 创建生产环境目录（如果不存在）
              ssh -p 56000 $server "mkdir -p $PRODUCTION_PATH"
              # 仅同步非 HTML 文件
              rsync -avz --exclude='*.html' -e 'ssh -p 56000' ./dist/ $server:$PRODUCTION_PATH/
            done
        
        # 2. 为 CDN 环境部署非 HTML 文件
        - >
            for server in $APPWEB_SERVER_GROUP; do
              # 创建 CDN 目录（如果不存在）
              ssh -p 56000 $server "mkdir -p $CDN_PATH"
              # 仅同步非 HTML 文件
              rsync -avz --exclude='*.html' -e 'ssh -p 56000' ./dist/ $server:$CDN_PATH/
            done
        
        # 3. 等待1秒确保非 HTML 资源已经可用
        - sleep 1
        
        # 4. 部署 HTML 文件到 production 环境
        - >
            for server in $APPWEB_SERVER_GROUP; do
              # 仅同步 HTML 文件
              rsync -avz --include='*.html' --include='*/' --exclude='*' -e 'ssh -p 56000' ./dist/ $server:$PRODUCTION_PATH/
            done
        
        # 5. 部署 HTML 文件到 CDN 环境
        - >
            for server in $APPWEB_SERVER_GROUP; do
              # 仅同步 HTML 文件
              rsync -avz --include='*.html' --include='*/' --exclude='*' -e 'ssh -p 56000' ./dist/ $server:$CDN_PATH/
            done
    environment:
        name: production
        url: https://appcdn.52tt.com/web/$CI_PROJECT_PATH_SLUG/index.html


    when: manual
    only:
        - main
    tags:
        - gitlab

    #生产环境下线
offline-production:
    stage: deploy
    cache:
        key: $CACHE_KEY
        paths:
            - dist/
    script:
        #执行下线命令
        - npm run offline
        #建立生产环境项目文件夹并上传
        - >
            for server in $APPWEB_SERVER_GROUP; do
              ssh -p 56000 $server mkdir -p $PRODUCTION_PATH
              rsync -avz --exclude '*.html' -e 'ssh -p 56000' --update ./dist/ $server:$PRODUCTION_PATH/
            done
        #建立CDN项目文件件并上传
        - >
            for server in $APPWEB_SERVER_GROUP; do
              ssh -p 56000 $server mkdir -p $CDN_PATH
              rsync -avz --exclude '*.html' -e 'ssh -p 56000' --update ./dist/ $server:$CDN_PATH/
            done
        # 同步HTML文件到临时生产环境路径
        - >
            for server in $APPWEB_SERVER_GROUP; do
              ssh -p 56000 $server mkdir -p $PRODUCTION_PATH
              rsync -avz -e 'ssh -p 56000' --include '*.html' --exclude '*' --update ./dist/ $server:PRODUCTION_PATH/
            done
        - >
            for server in $APPWEB_SERVER_GROUP; do
              ssh -p 56000 $server mkdir -p $CDN_PATH
              rsync -avz -e 'ssh -p 56000' --include '*.html' --exclude '*'  --update ./dist/ $server:$CDN_PATH/
            done
    when: manual
    only:
        - main
    tags:
        - gitlab

    #刷新qiniu cdn缓存的主要文件
refresh-main-files:
    image: cr.ttyuyin.com/frontend/registry/docker-service/qiniu-shell:2.3.4
    stage: cdn
    before_script:
        #登录
        - qshell account $QINIU_ACCESS_KEY $QINIU_SECRET_KEY frontend
    script:
        #创建待更新文件列表
        - |
            echo -e "${CDN_RELEASE_FOLDER_PATH}index.html\n\
            ${CDN_RELEASE_FOLDER_PATH_SHARE}index.html\n\
            ${CDN_RELEASE_FOLDER_PATH_HY_IOS_SHARE}index.html\n\
            ${CDN_RELEASE_FOLDER_PATH_HY_ANDROID}index.html\n\
            ${CDN_RELEASE_FOLDER_PATH_HY_ANDROID_SHARE}index.html\n\
            ${CDN_RELEASE_FOLDER_PATH_MK_IOS}index.html\n\
            ${CDN_RELEASE_FOLDER_PATH_MK_IOS_SHARE}index.html\n\
            ${CDN_RELEASE_FOLDER_PATH_MK_ANDROID}index.html\n\
            ${CDN_RELEASE_FOLDER_PATH_MK_ANDROID_SHARE}index.html\n\
            ${CDN_RELEASE_FOLDER_PATH}pendant.html\n\
            ${CDN_RELEASE_FOLDER_PATH_SHARE}pendant.html\n\
            ${CDN_RELEASE_FOLDER_PATH_HY_IOS_SHARE}pendant.html\n\
            ${CDN_RELEASE_FOLDER_PATH_HY_ANDROID}pendant.html\n\
            ${CDN_RELEASE_FOLDER_PATH_HY_ANDROID_SHARE}pendant.html\n\
            ${CDN_RELEASE_FOLDER_PATH_MK_IOS}pendant.html\n\
            ${CDN_RELEASE_FOLDER_PATH_MK_IOS_SHARE}pendant.html\n\
            ${CDN_RELEASE_FOLDER_PATH_MK_ANDROID}pendant.html\n\
            ${CDN_RELEASE_FOLDER_PATH_MK_ANDROID_SHARE}pendant.html" > main_files
        - cat main_files
        - qshell cdnrefresh -i main_files
    when: manual
    only:
        - main
    tags:
        - gitlab
    allow_failure: true

    #更新qiniu cdn缓存目录
refresh-folder:
    image: cr.ttyuyin.com/frontend/registry/docker-service/qiniu-shell:2.3.4
    stage: cdn
    before_script:
        #登录
        - qshell account $QINIU_ACCESS_KEY $QINIU_SECRET_KEY frontend
    script:
        - |
            echo -e "${CDN_RELEASE_FOLDER_PATH}\n\
            ${CDN_RELEASE_FOLDER_PATH_SHARE}\n\
            ${CDN_RELEASE_FOLDER_PATH_HY_IOS_SHARE}\n\
            ${CDN_RELEASE_FOLDER_PATH_HY_ANDROID}\n\
            ${CDN_RELEASE_FOLDER_PATH_HY_ANDROID_SHARE}\n\
            ${CDN_RELEASE_FOLDER_PATH_MK_IOS}\n\
            ${CDN_RELEASE_FOLDER_PATH_MK_IOS_SHARE}\n\
            ${CDN_RELEASE_FOLDER_PATH_MK_ANDROID}\n\
            ${CDN_RELEASE_FOLDER_PATH_MK_ANDROID_SHARE}" > cdn_folder
        - qshell cdnrefresh --dirs -i cdn_folder
    when: manual
    only:
        - main
    tags:
        - gitlab
    allow_failure: true

#刷新腾讯cdn文件
refresh-tc-url:
    image: cr.ttyuyin.com/frontend/registry/docker-service/python3.13-slim:tccli
    stage: cdn
    before_script:
        #登录
        - tccli configure set secretId $TC_CLOUD_SECRET_ID secretKey $TC_CLOUD_SECRET_KEY region ap-guangzhou
    script:
        # - tccli cdn PurgeUrlsCache --cli-unfold-argument --Urls "${CDN_RELEASE_FOLDER_PATH_SHARE}index.html"
        - |
           for i in {1..3}; do
            tccli cdn PurgeUrlsCache --cli-unfold-argument --Urls \
              "${CDN_RELEASE_FOLDER_PATH}index.html" \
              "${CDN_RELEASE_FOLDER_PATH_SHARE}index.html" \
              "${CDN_RELEASE_FOLDER_PATH_HY_IOS_SHARE}index.html" \
              "${CDN_RELEASE_FOLDER_PATH_HY_ANDROID}index.html" \
              "${CDN_RELEASE_FOLDER_PATH_HY_ANDROID_SHARE}index.html" \
              "${CDN_RELEASE_FOLDER_PATH_MK_IOS}index.html" \
              "${CDN_RELEASE_FOLDER_PATH_MK_IOS_SHARE}index.html" \
              "${CDN_RELEASE_FOLDER_PATH_MK_ANDROID}index.html" \
              "${CDN_RELEASE_FOLDER_PATH_MK_ANDROID_SHARE}index.html" \
              break || sleep 2
            done
    when: manual
    only:
        - main
    tags:
        - gitlab
    allow_failure: true

#刷新腾讯cdn目录
refresh-tc-folder:
    image: cr.ttyuyin.com/frontend/registry/docker-service/python3.13-slim:tccli
    stage: cdn
    before_script:
        #登录
        - tccli configure set secretId $TC_CLOUD_SECRET_ID secretKey $TC_CLOUD_SECRET_KEY region ap-guangzhou
    script:
        # - tccli cdn PurgePathCache --cli-unfold-argument --Paths $CDN_RELEASE_FOLDER_PATH_SHARE --FlushType delete
        - |
           for i in {1..3}; do
            tccli cdn PurgePathCache --cli-unfold-argument --Paths \
              "$CDN_RELEASE_FOLDER_PATH" \
              "$CDN_RELEASE_FOLDER_PATH_SHARE" \
              "$CDN_RELEASE_FOLDER_PATH_HY_IOS_SHARE" \
              "$CDN_RELEASE_FOLDER_PATH_HY_ANDROID" \
              "$CDN_RELEASE_FOLDER_PATH_HY_ANDROID_SHARE" \
              "$CDN_RELEASE_FOLDER_PATH_MK_IOS" \
              "$CDN_RELEASE_FOLDER_PATH_MK_IOS_SHARE" \
              "$CDN_RELEASE_FOLDER_PATH_MK_ANDROID" \
              "$CDN_RELEASE_FOLDER_PATH_MK_ANDROID_SHARE" \
              --FlushType delete && break || sleep 2
           done
    when: manual
    only:
        - main
    tags:
        - gitlab
    allow_failure: true
