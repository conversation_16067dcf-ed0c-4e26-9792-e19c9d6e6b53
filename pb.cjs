// 须知：
// 在项目根目录中执行 node ./pb2api $backendId $protoFileName
// backendId 默认为config.nodePath
// protoFileName 默认为 activity
// eg node ./pb.cjs puzzle-sign-2024
const { execSync } = require('node:child_process');
const assert = require('node:assert');
const fs = require('node:fs');
const path = require('node:path');
const process = require('node:process');

const config = fs.readFileSync('./src/config/index.js', 'utf-8');
const backendId = process.argv[2] || (config.match(/nodePath:\s*'([^']+)'/) || [])[1];
const protoFileName = process.argv[3] || 'activity';
assert(backendId, '请传入后端活动ID');

function rmFile(file) {
    const filePath = path.join(__dirname, file);
    if (fs.existsSync(filePath))
        fs.rmSync(filePath, { recursive: true, force: true });
}

async function run() {
    rmFile(backendId);
    execSync(`<NAME_EMAIL>:frontend/nodejs/activity/${backendId}.git`);
    assert(fs.existsSync(`./${backendId}`), '后端项目不存在');
    fs.copyFileSync(`./${backendId}/typings/controllers/${protoFileName}.d.ts`, 'src/api/api.d.ts');
    execSync(`npx pbjs -t json ./${backendId}/protos/service/${protoFileName}.proto -o  activity.json`);
    require('./pbmap2js.cjs')('activity');
    execSync('npx prettier --write src/api/index.js src/api/mockData.js src/api/api.d.ts');
    execSync('npx eslint src/api/index.js src/api/mockData.js src/api/api.d.ts  --fix');
    rmFile(backendId);
    rmFile('activity.json');
}

run().catch(console.error);
