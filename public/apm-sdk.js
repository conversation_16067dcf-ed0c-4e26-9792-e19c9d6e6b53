/**
 * @name getENV 根据URL获取当前环境
 * @returns {string} 返回当前环境 ['prod', 'gray', 'test', 'internal', 'dev']
 */
const getENV = () => {
    const urlString = window.location.href;
    const prod = [
        '.com/web/',
        '.net/web/',
        '.com/project/',
        '.net/project/',
        '.fun/web/',
        '.com/newweb',
        '.net/newweb',
        '.fun/newweb',
    ];
    const gray = ['.com/gray/', '.net/gray/', '.fun/gray/'];
    const testing = ['.com/testing/', '.net/testing/'];
    const internal = ['.com/internal/', '.net/internal/'];
    const checkIsMixed = (a, b) => a.some(v => b.includes(v));

    if (checkIsMixed(prod, urlString)) {
        return 'prod';
    }
    else if (checkIsMixed(gray, urlString)) {
        return 'gray';
    }
    else if (checkIsMixed(testing, urlString)) {
        return 'test';
    }
    else if (checkIsMixed(internal, urlString)) {
        return 'internal';
    }
    else {
        return 'dev';
    }
};
/**
 * @name isInENV 根据当前URL判断当前环境是否在指定环境列表中
 * @param {Array<string>} list 指定环境列表
 * ['prod', 'gray', 'test', 'internal', 'dev']
 */
function isInENV(list) {
    return list.includes(getENV());
}
/**
 * @name isInApp 获取是否在App内
 */
const isInApp = () => {
    try {
        TTJSBridge.invoke('operate', 'isInApp');
        return true;
    }
    catch {
        return false;
    }
};
/**
 * @name getVersion 获取App版本号
 */
const getVersion = () => {
    if (isInApp()) {
        try {
            return TTJSBridge.invoke('operate', 'getVersion');
        }
        catch {}
    }
};
/**
 * @name loadScriptSync 加载JS脚本
 */
const loadScriptSync = (src) => {
    let params = src;
    if (typeof src === 'string')
        params = [src];
    const scripts = [];
    for (let i = 0; i < params.length; i++) {
        scripts[i] = new Promise((resolve, reject) => {
            const HEAD
                = document.getElementsByTagName('head')[0]
                || document.documentElement;
            const script = document.createElement('script');
            script.type = 'text/javascript';
            script.onload = () => {
                resolve(script);
            };
            script.onerror = () => {
                reject(false);
            };
            script.src = params[i];
            HEAD.appendChild(script);
        });
    }
    return Promise.all(scripts);
};
/**
 * @name parseUrlQuery 获取url参数
 * @param {string} url
 * @returns {object} query
 */
const parseUrlQuery = (url = window.location.href) => {
    let uid = '';
    try {
        uid = TTJSBridge.invoke('data', 'getMyUid');
    }
    catch {}

    const result = /\?([^#]*)#?/.exec(url);
    if (!result || !result[1])
        return { uid };
    let paramsStr = result[1]; // 将 ? 后面的字符串提出来
    paramsStr = paramsStr.replace(/#/g, '');
    const paramsArr = paramsStr.split('&');
    const theRequest = paramsArr.reduce((pre, param) => {
        if (/=/.test(param)) {
            const splitIdx = param.indexOf('='); // 切割 key 和 value
            const key = param.substring(0, splitIdx);
            let val = param.substring(splitIdx + 1);
            val = decodeURIComponent(val); // url解码
            val = /^\d+$/.test(val) ? Number.parseFloat(val) : val; // 判断是否是数字
            pre[key] = val; // 默认取同名 key 的最后一个 value
        }
        else {
            if (param)
                pre[param] = true; // 处理没有 value 的参数
        }
        return pre;
    }, {});
    theRequest.uid = theRequest.uid || uid;

    return theRequest;
};
const initApm = () => {
    const { noApm, uid } = parseUrlQuery();
    const isTest = !isInENV(['prod']);
    if (isInENV(['prod', 'gray', 'test']) && !noApm) {
        window.pecker_config = {
            env: isTest ? 'test' : 'prod',
            app_id: 'tt-web-activity',
            plugins: [
                { plugin: 'WebVitalsPlugin', config: {} },
                { plugin: 'NetworkPlugin', config: {} },
                { plugin: 'ErrorLogPlugin', config: {} },
                { plugin: 'ResourcePlugin', config: {} },
            ],
            uid,
            country_code: 'CN',
            app_version: getVersion(),
            dsn: isTest
                ? 'https://test-cloud-ingest-all-gz.skyengine.com.cn/api/apm_notauth/v0/clientreport'
                : 'https://ingest-all-bj-gw.skyengine.com.cn/api/apm_notauth/v0/clientreport',
        };

        loadScriptSync(
            'https://appcdn.52tt.com/apm/quwan-appinsignt-apm-js-sdk/v2.0.8-alpha.1/pecker-base.js',
        );
        loadScriptSync(
            'https://appcdn.52tt.com/apm/quwan-appinsignt-apm-js-sdk/v2.0.8-alpha.1/pecker-main-min.js',
        );
    }
};
initApm();
