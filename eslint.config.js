import antfu from '@antfu/eslint-config';
import { FlatCompat } from '@eslint/eslintrc';

const compat = new FlatCompat();

export default antfu(
    {
        // enable UnoCSS support
        // https://unocss.dev/integrations/vscode
        unocss: true,
        formatters: {
            css: true,
        },
        stylistic: {
            indent: 4,
            quotes: 'single',
            semi: true,
        },
    },
    // 导入unplugin-auto-import的配置
    ...compat.config({
        extends: ['./.eslintrc-auto-import.json'],
    }),
    // 覆盖默认配置
    {
        rules: {
            'no-console': 'warn',
            'no-debugger': 'warn',
            'antfu/top-level-function': 'off',
            // 允许promise reject不传入error对象
            'prefer-promise-reject-errors': 'off',
            // 允许正则表达式使用字符编码
            'no-control-regex': 'off',
            // 单行块无需隐藏大括号
            'curly': 'off',
            'antfu/curly': 'off',
            // 允许"_"开头命名的变量未使用
            'no-unused-vars': 'off',
            'unused-imports/no-unused-vars': 'warn',
            // 无需自动删除未使用的import
            'unused-imports/no-unused-imports': 'off',
            // 允许promise中使用async
            'no-async-promise-executor': 'off',
            // 不强制使用const
            'prefer-const': 'off',
            'style/indent-binary-ops': 'off',
            // 允许非必要的return
            'no-useless-return': 'off',
            // 允许注释不使用空格
            'style/spaced-comment': 'off',
            // 允许ts定义的变量未使用
            'ts/no-unused-expressions': 'off',
            /**
             * vue相关配置
             */
            // vue文件template、script、style之间排序规则
            'vue/block-order': 'off',
            // 标签属性独占一行
            'vue/max-attributes-per-line': [
                'warn',
                {
                    singleline: 1,
                    multiline: 1,
                },
            ],
            // 允许空对标签
            'vue/html-self-closing': 'off',
            // 自定义组件不校验驼峰、短横线命名
            'vue/component-name-in-template-casing': 'off',
            // 标签右尖括号“>”不换行
            'vue/html-closing-bracket-newline': 'off',
            // 单行标签内容不换行
            'vue/singleline-html-element-content-newline': 'off',
            // v-for key值不必填
            'vue/valid-v-for': 'off',
            'vue/require-v-for-key': 'off',
            // 允许vue template的变量未使用
            'vue/no-unused-vars': 'off',
        },
        languageOptions: {
            globals: {
                TTJSBridge: true,
                eruda: true,
                VConsole: true,
                PageSpy: true,
                DataHarborPlugin: true,
                RRWebPlugin: true,
            },
        },
    },
    {
        ignores: [
            '.vscode',
            '.husky',
            'build',
            'public',
            'src/utils',
            '*.yml',
            '*.md',
            '*.json',
            'stats.html',
            'pb.cjs',
            'pb-p.cjs',
            'pbmap2js.cjs',
            'reward-tool.js',
            'src/router/index.js',
        ],
    },
);
