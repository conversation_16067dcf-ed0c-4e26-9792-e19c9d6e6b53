{
    // 控制相关文件嵌套展示
    "explorer.fileNesting.enabled": true,
    "explorer.fileNesting.patterns": {
        "pb.cjs": "pb-p.cjs,pbmap2js.cjs",
        "package.json": "pnpm-lock.yaml,pnpm-workspace.yaml,yarn.lock,package-lock.json,jsconfig.json",
        "eslint.config.js": ".editorconfig,.prettierrc.json,.eslintcache,.eslintrc-auto-import.json",
        "index.html": "*.html",
        "auto-imports.d.ts": "*.d.ts"
    },

    // Enable the ESlint flat config support
    "eslint.useFlatConfig": true,

    // Auto fix
    "editor.formatOnSave": false,
    "editor.codeActionsOnSave": {
        "source.fixAll": "explicit",
        "source.organizeImports": "never"
    },

    // Silent the stylistic rules in you IDE, but still auto fix them
    "eslint.rules.customizations": [
        { "rule": "style/*", "severity": "off" },
        { "rule": "format/*", "severity": "off" },
        { "rule": "*-indent", "severity": "off" },
        { "rule": "*-spacing", "severity": "off" },
        { "rule": "*-spaces", "severity": "off" },
        { "rule": "*-order", "severity": "off" },
        { "rule": "*-dangle", "severity": "off" },
        { "rule": "*-newline", "severity": "off" },
        { "rule": "*quotes", "severity": "off" },
        { "rule": "*semi", "severity": "off" }
    ],

    // Enable eslint for all supported languages
    "eslint.validate": [
        "javascript",
        "javascriptreact",
        "typescript",
        "typescriptreact",
        "vue",
        "html",
        "markdown",
        "json",
        "jsonc",
        "yaml",
        "toml",
        "xml",
        "gql",
        "graphql",
        "astro",
        "css",
        "less",
        "scss",
        "pcss",
        "postcss"
    ],

    // Specify the UI library you need to prompt
    "common-intellisense.ui": ["vant4"]
}
