{
    // 路由页
    "v4page": {
        "scope": "vue",
        "prefix": "v4page",
        "body": [
            "<route lang=\"json\">",
            "{",
            "    \"name\": \"${TM_FILENAME_BASE/(.*)/${1:/pascalcase}/}\",",
            "    \"path\": \"/${TM_FILENAME_BASE/([a-z0-9]+)?([A-Z])/$1${1:+-}${2:/downcase}/g}\",",
            "    \"meta\": {",
            "        \"title\": \"${TM_FILENAME_BASE/([a-z0-9]+)?([A-Z])/$1${1:+-}${2:/downcase}/g}\"",
            "    }",
            "}",
            "</route>",
            "",
            "<template>",
            "    <page-container>",
            "        <div class=\"${TM_FILENAME_BASE/([a-z0-9]+)?([A-Z])/$1${1:+-}${2:/downcase}/g}\">",
            "            ${TM_FILENAME}",
            "        </div>",
            "    </page-container>",
            "</template>",
            "",
            "<script setup name=\"${TM_FILENAME_BASE/(.*)/${1:/pascalcase}/}\">",
            "",
            "</script>",
            "",
            "<style lang=\"less\" scoped>",
            ".${TM_FILENAME_BASE/([a-z0-9]+)?([A-Z])/$1${1:+-}${2:/downcase}/g} {",
            "    min-height: 100%;",
            "    background-color: #fff;",
            "}",
            "</style>",
            "",
        ],
        "description": "mfv4模板路由页面",
    },
    // 组件
    "v4comp": {
        "scope": "vue",
        "prefix": "v4comp",
        "body": [
            "<template>",
            "    <div class=\"${TM_FILENAME_BASE/([a-z0-9]+)?([A-Z])/$1${1:+-}${2:/downcase}/g}\">",
            "        ${TM_FILENAME}",
            "    </div>",
            "</template>",
            "",
            "<script setup>",
            "",
            "</script>",
            "",
            "<style lang=\"less\" scoped>",
            ".${TM_FILENAME_BASE/([a-z0-9]+)?([A-Z])/$1${1:+-}${2:/downcase}/g}{",
            "",
            "}",
            "</style>",
            "",
        ],
        "description": "mfv4模板组件",
    },
    // 弹窗
    "v4modal": {
        "scope": "vue",
        "prefix": "v4modal",
        "body": [
            "<template>",
            "    <modal-container",
            "        v-model:show=\"isShow\"",
            "        :close-on-click-overlay=\"true\">",
            "        <div class=\"${TM_FILENAME_BASE/([a-z0-9]+)?([A-Z])/$1${1:+-}${2:/downcase}/g}\">",
            "            ${TM_FILENAME}",
            "        </div>",
            "    </modal-container>",
            "</template>",
            "",
            "<script setup>",
            "const isShow = ref(false);",
            "",
            "useEventBus('${TM_FILENAME_BASE/([a-z0-9]+)?([A-Z])/$1${1:+-}${2:/downcase}/g}').on(({ show = true }) => {",
            "    isShow.value = show;",
            "});",
            "</script>",
            "",
            "<style lang=\"less\" scoped>",
            ".${TM_FILENAME_BASE/([a-z0-9]+)?([A-Z])/$1${1:+-}${2:/downcase}/g} {",
            "    width: 250px;",
            "    height: 250px;",
            "    border-radius: 15px;",
            "    background-color: #fff;",
            "}",
            "</style>",
            "",
        ],
        "description": "mfv4模板弹窗",
    },
    // 半屏弹窗
    "v4popup": {
        "scope": "vue",
        "prefix": "v4popup",
        "body": [
            "<template>",
            "    <popup-container",
            "        v-model:show=\"isShow\"",
            "        :close-on-click-overlay=\"true\">",
            "        <div class=\"${TM_FILENAME_BASE/([a-z0-9]+)?([A-Z])/$1${1:+-}${2:/downcase}/g}\">",
            "            ${TM_FILENAME}",
            "        </div>",
            "    </popup-container>",
            "</template>",
            "",
            "<script setup>",
            "const isShow = ref(false);",
            "",
            "useEventBus('${TM_FILENAME_BASE/([a-z0-9]+)?([A-Z])/$1${1:+-}${2:/downcase}/g}').on(({ show = true }) => {",
            "    isShow.value = show;",
            "});",
            "</script>",
            "",
            "<style lang=\"less\" scoped>",
            ".${TM_FILENAME_BASE/([a-z0-9]+)?([A-Z])/$1${1:+-}${2:/downcase}/g} {",
            "    width: 375px;",
            "    height: 500px;",
            "    padding-top: 38px;",
            "    background-color: #fff;",
            "}",
            "</style>",
            "",
        ],
        "description": "mfv4模板半屏弹窗",
    },
}
