#!/usr/bin/env node
/* eslint-disable eslint-comments/no-unlimited-disable */

/* eslint-disable */

'use strict';

import path from 'node:path';
import fs from 'node:fs';
import axios from 'axios';
import serialize from 'serialize-javascript';

let [rewardUrl, developer, rewardSheetIndex, msgSheetIndex] = process.argv.slice(2);
/**
 * https://q9jvw0u5f5.feishu.cn/sheets/shtcno1KoZj7by2nrjDTtR5QGQi // 资源url
 * shtcno1KoZj7by2nrjDTtR5QGQi // excelId
 */
rewardUrl = rewardUrl || 'https://q9jvw0u5f5.feishu.cn/wiki/CKkrwuxDji27SKkjn8ecF1J0nsc'; // 资源url
developer = +developer || 2; // 1-后端使用 2-前端使用
rewardSheetIndex = +rewardSheetIndex || 0; // 默认第1个sheetId为资源奖励
msgSheetIndex = +msgSheetIndex || 1; // 默认第2个sheetId为消息配置
let spreadsheetId = getExcelId(); // excelId
const rootDir = process.cwd();
const imageFolder = path.join(rootDir, 'src/assets/img/rewards/'); // 前端图片文件夹
const rewardFolder = path.join(rootDir, '/src/config/'); // 前端配置文件夹
const imageType = '.png'; // 前端图片类型
let token;

// 礼物类型
const REWARD_TYPE = {
    PACKAGE: {
        type: 'package',
        name: '包裹',
        unit: '个',
    },
    MEDAL: {
        type: 'medal',
        name: '勋章',
        unit: '天',
    },
    HEADWEAR: {
        type: 'headwear',
        name: '麦位框',
        unit: '天',
    },
    MOUNT: {
        type: 'mount',
        name: '坐骑',
        unit: '天',
    },
    ENTER_ROOM_EFFECT: {
        type: 'mount',
        name: '进房板',
        unit: '天',
    },
    FLUTTER: {
        type: 'flutter',
        name: '主页飘',
        unit: '天',
    },
    BG: {
        type: 'bg',
        name: '房间背景',
        unit: '天',
    },
    UGC_BG: {
        type: 'ugc_bg',
        name: 'UGC房间背景',
        unit: '天',
    },
    FANS_PLATE: {
        type: 'fans_plate',
        name: '粉丝团铭牌',
        unit: '天',
    },
    USER_PLATE: {
        type: 'user_plate',
        name: '个人铭牌',
        unit: '天',
    },
    OFFICIAL_CERT: {
        type: 'official_cert',
        name: '大V认证',
        unit: '天',
    },
    SUPER_PLAYER: {
        type: 'super_player',
        name: '超级会员',
        unit: '天',
    },
    POP_WINDOW: {
        type: 'pop_window',
        name: '房间弹窗',
        unit: '次',
    },
    FLOW_CARD: {
        type: 'flow_card',
        name: '流量卡',
        unit: '张',
    },
    PRESENT_WALL_EFFECT: {
        type: 'present_wall_effect',
        name: '礼物墙光效',
        unit: '天',
    },
    BREAKING_NEWS: {
        type: 'breaking_news',
        name: '全服公告',
        unit: '条',
    },
    ANCHOR_CERT: {
        type: 'anchor_cert',
        name: '主播大V认证',
        unit: '天',
    },
    PRESENT_PRIVILEGE: {
        type: 'present_privilege',
        name: '礼物赠送权',
        unit: '天',
    },
    PRESENT_EFFECT: {
        type: 'present_effect',
        name: '送礼特效',
        unit: '次',
    },
    OTHER: {
        type: 'other',
        name: '其它',
        unit: '天',
    },
};

const rewardSheetFieldMaps = {
    resourceId: '资源ID(唯一)',
    imageId: '图片资源ID',
    remark1: '备注1',
    module: '应用模块',
    rewardType: '奖励类型',
    specialType: '特殊类型',
    customText: '自定义装扮文案',
    packageType: '包裹类型',
    packagePrice: '包裹价值',
    testingName: '云测名称',
    testingId: '云测ID',
    testingSecretId: '云测包裹风控ID',
    testingSecretKey: '云测包裹风控KEY',
    testingVersion: '云测奖励等级',
    prodName: '正式名称',
    prodId: '正式ID',
    prodImage: '正式设计资源图',
    prodSecretId: '正式包裹风控ID',
    prodSecretKey: '正式包裹风控KEY',
    prodVersion: '正式奖励等级',
    remark2: '备注2',
    remark3: '备注3',
    testingItemId: '云测礼物ID',
    prodItemId: '正式礼物ID',
};

const msgSheetFieldMaps = {
    resourceId: '资源ID(唯一)',
    module: '应用模块',
    scene: '推送场景',
    pushType: '推送方式',
    content: '消息内容',
    jumpTab: '点击跳转定位',
    jumpUrl: '跳转链接',
    remark1: '备注1',
};

function getExcelId() {
    try {
        const list = rewardUrl.split('/');
        let excelId = list[list.length - 1];
        if (excelId.includes('?')) {
            excelId = excelId.split('?')[0];
        }
        return excelId;
    } catch (error) {
        throw 'ERROR: 请检查资源url是否正确！';
    }
}

const REWARD_C2E_MAP = (function getC2E() {
    const rMap = {};
    for (const key in REWARD_TYPE) {
        const item = REWARD_TYPE[key];
        rMap[item.name] = item.type;
    }
    return rMap;
})();
const rewardTypes = Object.keys(REWARD_C2E_MAP);

async function getToken() {
    const tokenInfo = await axios({
        url: 'https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal',
        method: 'post',
        data: {
            app_id: 'cli_a3e9cd7ec5fd1013',
            app_secret: 'QovCecIAGCNqaa7rIcvLHeYzPGrOBUdg',
        },
    });
    const token = `Bearer ${tokenInfo.data.tenant_access_token}`;
    return token;
}

async function getWikiToken() {
    token = await getToken();
    try {
        const sheetInfo = await axios({
            url: `https://open.feishu.cn/open-apis/wiki/v2/spaces/get_node`,
            method: 'get',
            headers: {
                Authorization: token,
            },
            params: {
                token: spreadsheetId,
            },
        });
        return sheetInfo.data.data.node.obj_token;
    } catch (error) {
        console.error(` getWikiToken fail: ${error} `);
    }
}

async function getSheet(sheetIndex) {
    token = await getToken();
    const sheetInfo = await axios({
        url: `https://open.feishu.cn/open-apis/sheets/v3/spreadsheets/${spreadsheetId}/sheets/query`,
        method: 'get',
        headers: {
            Authorization: token,
        },
    });
    const sheetTable = sheetInfo.data.data.sheets.map((sheet) => [sheet.title, sheet.sheet_id]);
    console.table(sheetTable);
    const sheetIdLIst = sheetInfo.data.data.sheets.map((sheet) => sheet.sheet_id);
    return sheetIdLIst[sheetIndex];
}

async function getSheetData(sheetIndex) {
    try {
        const sheetId = await getSheet(sheetIndex);
        if (!sheetId) {
            return [];
        }
        const res = await axios({
            url: `https://open.feishu.cn/open-apis/sheets/v2/spreadsheets/${spreadsheetId}/values_batch_get`,
            method: 'get',
            headers: {
                Authorization: token,
                'Content-Type': 'application/json; charset=utf-8',
            },
            params: {
                ranges: sheetId,
                valueRenderOption: 'FormattedValue',
            },
        });
        const datas = res.data.data.valueRanges[0].values;
        return datas;
    } catch (err) {
        console.error(` getSheetData fail: ${err} `);
    }
}

async function getResourceList(sheetIndex, sheetFieldMaps) {
    const values = await getSheetData(sheetIndex);
    if (!values || !values.length) {
        return [];
    }
    const needSerializeFieldsMap = {
        0: '资源ID(唯一)',
    };
    const sheetFieldNames = Object.values(sheetFieldMaps);
    values[0].forEach((item, index) => {
        if (sheetFieldNames.includes(item)) {
            needSerializeFieldsMap[index] = item;
        }
    });
    const reversedSheetFieldMaps = {};
    for (const [key, value] of Object.entries(sheetFieldMaps)) {
        reversedSheetFieldMaps[value] = key;
    }
    const list = [];
    for (let index = 1; index < values.length; index++) {
        const datas = values[index];
        if (datas && datas.length && !datas[0]) {
            continue;
        }
        const dataObj = {};
        for (let index = 0; index < datas.length; index++) {
            if (!needSerializeFieldsMap[index] || !datas[index]) {
                continue;
            }
            if (typeof datas[index] === 'string') {
                const item = datas[index].replace(/\s/g, '');
                dataObj[reversedSheetFieldMaps[needSerializeFieldsMap[index]]] = item;
            } else {
                dataObj[reversedSheetFieldMaps[needSerializeFieldsMap[index]]] = datas[index];
            }
        }
        if (Object.keys(dataObj).length) {
            list.push(dataObj);
        }
    }
    if (list.length !== new Set(list.map((l) => l.resourceId)).size) {
        throw 'ERROR: 请检查资源ID是否唯一！';
    }
    return list;
}

function formatSpecialType(specialType) {
    const typeStr = {
        男: 'sex: 1',
        女: 'sex: 0',
        'CP(男)': 'cp: 1, sex: 1',
        'CP(女)': 'cp: 1, sex: 0',
        融合: 'fusion: 1',
        CP: 'cp: 1',
    };
    return typeStr[specialType] || '';
}

async function downloadsImageResources(rewardList) {
    if (!fs.existsSync(imageFolder)) {
        fs.mkdirSync(
            imageFolder,
            {
                recursive: true,
            },
            (error) => {
                if (error) throw error;
            },
        );
    }
    try {
        for (const reward of rewardList) {
            if (reward.prodImage && reward.prodImage.fileToken) {
                const res = await axios({
                    url: `https://open.feishu.cn/open-apis/drive/v1/medias/${reward.prodImage.fileToken}/download`,
                    method: 'get',
                    headers: {
                        Authorization: token,
                    },
                    responseType: 'arraybuffer',
                });
                const imageId = reward.imageId || reward.resourceId;
                fs.writeFile(
                    `${imageFolder}${imageId}${imageType}`,
                    Buffer.from(res.data),
                    (error) => {
                        if (error) {
                            console.error(error);
                            return;
                        }
                        console.log(` ${imageId}${imageType} 已被保存 `);
                    },
                );
            }
        }
        console.log(` downloadsImageResourcesEnd `);
    } catch (error) {
        console.error(` downloadsImageResources fail: ${error} `);
    }
}

async function generateRewardConfigBackEnd() {
    const rewardList = await getResourceList(rewardSheetIndex, rewardSheetFieldMaps);
    if (!rewardList || !rewardList.length) {
        return;
    }
    const REWARD_CONFIG = {};
    let testingSecretId, testingSecretKey, prodSecretId, prodSecretKey, module;
    for (const reward of rewardList) {
        try {
            let type = REWARD_TYPE.OTHER.type;
            let unit = REWARD_TYPE.OTHER.unit;
            let mark = reward.rewardType;
            if (rewardTypes.includes(reward.rewardType)) {
                type = REWARD_C2E_MAP[reward.rewardType];
                unit = REWARD_TYPE[REWARD_C2E_MAP[reward.rewardType].toUpperCase()].unit;
                mark = REWARD_TYPE[REWARD_C2E_MAP[reward.rewardType].toUpperCase()].name.includes(
                    REWARD_TYPE.BG.name,
                )
                    ? REWARD_TYPE.BG.name
                    : reward.rewardType;
            }
            let funcParam = 'time';
            const options = 'options';
            module = reward.module || module;
            let funcBodyStr = `
                resourceId: '${reward.resourceId}',
                name: isTest ? '${reward.testingName}' : '${reward.prodName}',
                id: isTest ? '${reward.testingId}' : '${reward.prodId}',
                module: '${module}',
                type: '${type}',
                unit: '${unit}',
                mark: '${mark}',
            `;
            testingSecretId = reward.testingSecretId || testingSecretId;
            testingSecretKey = reward.testingSecretKey || testingSecretKey;
            prodSecretId = reward.prodSecretId || prodSecretId;
            prodSecretKey = reward.prodSecretKey || prodSecretKey;
            if (REWARD_TYPE.PACKAGE.type === REWARD_C2E_MAP[reward.rewardType]) {
                funcParam = 'num';
                funcBodyStr += `business_id: isTest ? '${testingSecretId}' : '${prodSecretId}',
                    business_secret: isTest ? '${testingSecretKey}' : '${prodSecretKey}',
                    package_type: '${reward.packageType}',
                    package_price: '${
                        reward.packagePrice && reward.packagePrice.toString().includes('元')
                            ? Number.parseFloat(reward.packagePrice) * 100
                            : reward.packagePrice
                    }',
                `;
            } else if (REWARD_TYPE.FLUTTER.type === REWARD_C2E_MAP[reward.rewardType]) {
                funcBodyStr += `version: isTest ? '${reward.testingVersion || 'v1'}' : '${
                    reward.prodVersion || 'v1'
                }',`;
            } else if (reward.testingVersion || reward.prodVersion) {
                funcBodyStr += `version: isTest ? '${
                    reward.testingVersion || reward.prodVersion
                }' : '${reward.prodVersion || reward.testingVersion}',`;
            }
            if (reward.testingItemId || reward.prodItemId) {
                funcBodyStr += `item_id: isTest ? '${reward.testingItemId}' : '${reward.prodItemId}',`;
            }
            funcBodyStr += reward.customText ? `custom_text: '${reward.customText}',` : '';
            funcBodyStr += reward.remark1 ? `remark1: '${reward.remark1}',` : '';
            funcBodyStr += reward.remark2 ? `remark2: '${reward.remark2}',` : '';
            funcBodyStr += reward.remark3 ? `remark3: '${reward.remark3}',` : '';
            funcBodyStr += `${funcParam},`;
            funcBodyStr += formatSpecialType(reward.specialType)
                ? `${formatSpecialType(reward.specialType)},`
                : '';
            funcBodyStr += `...${options},`;
            const rewardFunc = new Function(funcParam, options, `return {${funcBodyStr}};`);
            REWARD_CONFIG[reward.resourceId] = rewardFunc;
        } catch (error) {
            console.error('serialized Failure');
            console.error(error, reward);
            continue;
        }
    }
    let serializedRewardFuncString = serialize(REWARD_CONFIG, {
        space: '\n',
    });
    serializedRewardFuncString = serializedRewardFuncString.replace(/anonymous|undefined/g, '');
    fs.writeFile(
        `${rootDir}/reward.js`,
        `"use strict";\n\n` +
            `const isTest = !['gray', 'production'].includes(process.env.NODE_ENV);\n\n` +
            `module.exports = ${serializedRewardFuncString}`,
        (error) => {
            if (error) throw error;
            console.log('REWARD_CONFIG has been written to reward.js');
        },
    );
}

async function generateRewardConfigFrontEnd() {
    const rewardList = await getResourceList(rewardSheetIndex, rewardSheetFieldMaps);
    const REWARD_CONFIG_LIST = [];
    const REWARD_CONFIG_MAP = {};
    let module;
    for (const reward of rewardList) {
        module = reward.module || module;
        let type = REWARD_TYPE.OTHER.type;
        let unit = REWARD_TYPE.OTHER.unit;
        let mark = reward.rewardType;
        if (rewardTypes.includes(reward.rewardType)) {
            type = REWARD_C2E_MAP[reward.rewardType];
            mark = REWARD_TYPE[REWARD_C2E_MAP[reward.rewardType].toUpperCase()].name.includes(
                REWARD_TYPE.BG.name,
            )
                ? REWARD_TYPE.BG.name
                : reward.rewardType;
            unit = REWARD_TYPE[REWARD_C2E_MAP[reward.rewardType].toUpperCase()].unit;
        }
        let item = {
            specialType: reward.specialType,
            name: reward.prodName || reward.testingName,
            type,
            mark,
            module,
            unit,
            resource_id: reward.resourceId,
            image: reward.imageId
                ? `${reward.imageId}${imageType}`
                : `${reward.resourceId}${imageType}`,
            remark1: reward.remark1,
            remark2: reward.remark2,
            remark3: reward.remark3,
        };
        if (REWARD_TYPE.PACKAGE.type === REWARD_C2E_MAP[reward.rewardType]) {
            item = {
                ...item,
                package_type: reward.packageType,
                package_price:
                    reward.packagePrice && reward.packagePrice.toString().includes('元')
                        ? Number.parseFloat(reward.packagePrice) * 100
                        : reward.packagePrice,
            };
        }
        REWARD_CONFIG_LIST.push(item);
        REWARD_CONFIG_MAP[reward.resourceId] = item;
    }
    const serializedRewardListString = JSON.stringify(REWARD_CONFIG_LIST, null, 4);
    const serializedRewardMapString = JSON.stringify(REWARD_CONFIG_MAP, null, 4);
    fs.writeFile(
        `${rewardFolder}reward.js`,
        `export const rewardList = ${serializedRewardListString}\n\n` +
            `export const rewardMap = ${serializedRewardMapString}\n\n`,
        (error) => {
            if (error) throw error;
            console.log('REWARD_CONFIG has been written to reward.js');
        },
    );
    downloadsImageResources(rewardList);
}

const PUSH_TYPE = {
    消息助手: 1,
    主播服务号: 2,
};
async function generateMsgConfigBackEnd() {
    const msgList = await getResourceList(msgSheetIndex, msgSheetFieldMaps);
    if (!msgList || !msgList.length) {
        return;
    }
    const MSG_CONFIG_MAP = {};
    const regexBracket = /【[^X\u3011]*X[^\u3011]*】/gi;
    const regexParentheses = /[(（][^X\uFF09]*X[^\uFF09]*[）)]/gi;
    const pattern = /[\u4E00-\u9FA5>]*([\u4E00-\u9FA5]*)>/;
    for (const msg of msgList) {
        if (!msg.resourceId || !msg.content) {
            continue;
        }
        const contentIsArray = Array.isArray(msg.content);
        const originContent = contentIsArray
            ? (msg.content[0] && msg.content[0].text) + (msg.content[1] && msg.content[1].text)
            : msg.content;
        const replacedTextWithBrackets = originContent.replace(regexBracket, '【%s】');
        const content = replacedTextWithBrackets
            .replace(regexParentheses, '%s')
            .replace(/x+/gi, '%s');
        const hightlight = contentIsArray
            ? msg.content[1] && msg.content[1].text
            : !content.includes('>')
            ? undefined
            : content.match(pattern)
            ? content.match(pattern)[0]
            : undefined;
        let url, jumpUrl;
        if (msg.jumpTab) {
            if (msg.jumpUrl) {
                if (
                    Array.isArray(msg.jumpUrl) &&
                    msg.jumpUrl.length &&
                    msg.jumpUrl[0] &&
                    msg.jumpUrl[0].link
                ) {
                    url = msg.jumpUrl[0].link;
                } else {
                    jumpUrl = msg.jumpUrl;
                }
            }
        }
        MSG_CONFIG_MAP[msg.resourceId] = {
            content,
            url,
            jumpUrl,
            contentType: msg.jumpTab || hightlight ? 2 : 1,
            pushType: PUSH_TYPE[msg.pushType] || PUSH_TYPE.消息助手,
            hightlight,
        };
    }
    const serializedMsgMapString = JSON.stringify(MSG_CONFIG_MAP, null, 4);
    fs.writeFile(
        `${rootDir}/msg.js`,
        `"use strict";\n\n` + `module.exports = ${serializedMsgMapString}\n\n`,
        (error) => {
            if (error) throw error;
            console.log('MSG_CONFIG has been written to msg.js');
        },
    );
}

async function generateRewardConfig() {
    // 兼容wiki和excel两种资源
    if (rewardUrl.includes('wiki')) {
        spreadsheetId = await getWikiToken();
    }
    if (developer === 1) {
        await generateRewardConfigBackEnd();
        await generateMsgConfigBackEnd();
    } else {
        await generateRewardConfigFrontEnd();
    }
}

generateRewardConfig();
