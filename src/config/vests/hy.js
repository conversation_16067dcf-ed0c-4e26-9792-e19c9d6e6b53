/**
 * @description 目前有tt(tt语音),hy(欢游),mk(麦可),mj(谜境)4个马甲包配置
 * @description ios，android表示当前系统，tt其实是统一，但是hy跟mk需要
 * @param api: 后台服务地址
 * @param node: 活动的node服务地址
 * @param node_common: 公共的node服务地址（短链、口令、微信签名等）
 * @param web: 活动页面地址
 * @param activeAppOptions: 唤醒包的配置表
 * @param jsBridgeProtocol: 跟客户端对接的JSBridge协议头
 * @param jsBridgePreview: 跟客户端对接的JSBridge协议头+域名
 * @param website: 对应当前马甲包的官网
 * @param app: 版本页面地址
 * @param platformApp app马甲分享图
 */

export default {
    app_name: '欢游',
    api_ios: {
        prod: '//api.tses.net/',
        gray: '//api.tses.net/',
        testing: '//testing-tt-web-tc.ttyuyin.com/',
        dev: '//testing-tt-web-tc.ttyuyin.com/',
    },
    api_android: {
        prod: '//zy-api.rzhushou.com/',
        gray: '//zy-api.rzhushou.com/',
        testing: '//testing-tt-web-tc.ttyuyin.com/',
        dev: '//testing-tt-web-tc.ttyuyin.com/',
    },
    node_ios: {
        prod: '//node-unify.tses.net/activity-production/',
        gray: '//node-unify.tses.net/activity-testing/',
        testing: '//testing-tt-web-tc.ttyuyin.com/activity-testing/',
        dev: '//testing-tt-web-tc.ttyuyin.com/activity-testing/',
    },
    node_android: {
        prod: '//node-unify.rzhushou.com/activity-production/',
        gray: '//node-unify.rzhushou.com/activity-testing/',
        testing: '//testing-tt-web-tc.ttyuyin.com/activity-testing/',
        dev: '//testing-tt-web-tc.ttyuyin.com/activity-testing/',
    },
    node_room_ios: {
        prod: '//node-room.52tt.com/common-production/',
        gray: '//node-room.52tt.com/common-production/',
        testing: '//testing-tt-web-tc.ttyuyin.com/common-testing/',
        dev: '//testing-tt-web-tc.ttyuyin.com/common-testing/',
    },
    node_room_android: {
        prod: '//node-room.52tt.com/common-production/',
        gray: '//node-room.52tt.com/common-production/',
        testing: '//testing-tt-web-tc.ttyuyin.com/common-testing/',
        dev: '//testing-tt-web-tc.ttyuyin.com/common-testing/',
    },
    node_common_ios: {
        prod: '//node-unify.tses.net/common-production/',
        gray: '//node-unify.tses.net/common-production/',
        testing: '//testing-tt-web-tc.ttyuyin.com/common-testing/',
        dev: '//testing-tt-web-tc.ttyuyin.com/common-testing/',
    },
    node_common_android: {
        prod: '//node-unify.rzhushou.com/common-production/',
        gray: '//node-unify.rzhushou.com/common-production/',
        testing: '//testing-tt-web-tc.ttyuyin.com/common-testing/',
        dev: '//testing-tt-web-tc.ttyuyin.com/common-testing/',
    },
    avatar_ios: {
        prod: 'https://avatar.tses.net/v2/',
        gray: 'https://avatar.tses.net/v2/',
        testing: 'https://testing-avatar.ttyuyin.com/v2/',
        dev: 'https://testing-avatar.ttyuyin.com/v2/',
    },
    avatar_android: {
        prod: 'https://zy-avatar.rzhushou.com/v2/',
        gray: 'https://zy-avatar.rzhushou.com/v2/',
        testing: 'https://testing-avatar.ttyuyin.com/v2/',
        dev: 'https://testing-avatar.ttyuyin.com/v2/',
    },
    web_ios: {
        dev: 'https://app.tses.net/testing/frontend-web-activity-',
        internal: 'https://app.tses.net/internal/frontend-web-activity-',
        testing: 'https://app.tses.net/testing/frontend-web-activity-',
        gray: 'https://app.tses.net/gray/frontend-web-activity-',
        prod: 'https://appcdn.tses.net/web/frontend-web-activity-',
    },
    web_ios_share: {
        dev: 'https://app.tses.net/testing/frontend-web-activity-',
        internal: 'https://app.tses.net/internal/frontend-web-activity-',
        testing: 'https://app.tses.net/testing/frontend-web-activity-',
        gray: 'https://app.tses.net/gray/frontend-web-activity-',
        prod: 'https://cdn.tses.net/web/frontend-web-activity-',
    },
    web_android: {
        dev: 'https://zy-app.rzhushou.com/testing/frontend-web-activity-',
        internal: 'https://zy-app.rzhushou.com/internal/frontend-web-activity-',
        testing: 'https://zy-app.rzhushou.com/testing/frontend-web-activity-',
        gray: 'https://zy-app.rzhushou.com/gray/frontend-web-activity-',
        prod: 'https://zy-appcdn.rzhushou.com/web/frontend-web-activity-',
    },
    web_android_share: {
        dev: 'https://zy-app.rzhushou.com/testing/frontend-web-activity-',
        internal: 'https://zy-app.rzhushou.com/internal/frontend-web-activity-',
        testing: 'https://zy-app.rzhushou.com/testing/frontend-web-activity-',
        gray: 'https://zy-app.rzhushou.com/gray/frontend-web-activity-',
        prod: 'https://zy-cdn.rzhushou.com/web/frontend-web-activity-',
    },
    web_ios_host: {
        dev: 'https://app.tses.net',
        internal: 'https://app.tses.net',
        testing: 'https://app.tses.net',
        gray: 'https://app.tses.net',
        prod: 'https://appcdn.tses.net',
    },
    web_ios_share_host: {
        dev: 'https://app.tses.net',
        internal: 'https://app.tses.net',
        testing: 'https://app.tses.net',
        gray: 'https://app.tses.net',
        prod: 'https://cdn.tses.net',
    },
    web_android_host: {
        dev: 'https://zy-app.rzhushou.com',
        internal: 'https://zy-app.rzhushou.com',
        testing: 'https://zy-app.rzhushou.com',
        gray: 'https://zy-app.rzhushou.com',
        prod: 'https://zy-appcdn.rzhushou.com',
    },
    web_android_share_host: {
        dev: 'https://zy-app.rzhushou.com',
        internal: 'https://zy-app.rzhushou.com',
        testing: 'https://zy-app.rzhushou.com',
        gray: 'https://zy-app.rzhushou.com',
        prod: 'https://zy-cdn.rzhushou.com',
    },
    app_ios: {
        dev: 'https://app.tses.net/testing/frontend-web-assist-',
        internal: 'https://app.tses.net/internal/frontend-web-assist-',
        testing: 'https://app.tses.net/testing/frontend-web-assist-',
        gray: 'https://app.tses.net/gray/frontend-web-assist-',
        prod: 'https://appcdn.tses.net/web/frontend-web-assist-',
    },
    app_android: {
        dev: 'https://zy-app.rzhushou.com/testing/frontend-web-assist-',
        internal: 'https://zy-app.rzhushou.com/internal/frontend-web-assist-',
        testing: 'https://zy-app.rzhushou.com/testing/frontend-web-assist-',
        gray: 'https://zy-app.rzhushou.com/gray/frontend-web-assist-',
        prod: 'https://zy-appcdn.rzhushou.com/web/frontend-web-assist-',
    },
    activeAppOptions: {
        androidLink: 'm://tses.net',
        universal: 'https://ul.tses.net',
        appstore: 'https://apps.apple.com/cn/app/id1612257488',
        fallback: 'https://a.app.qq.com/o/simple.jsp?pkgname=com.sabac.hy',
        official:
            'https://zy-cdntt.rzhushou.com/packages/HuanYou/Mi4wLjBAMjY5NQ/official/huanyou.apk',
        createPsw: true,
        marketId: 2,
    },
    jsBridgeProtocol: 'tyf://',
    jsBridgePreview: 'tyf://m.tses.net',
    website_ios: 'https://zy.tses.net',
    website_android: 'https://zy.rzhushou.com',
    platformApp: {
        type: 'TT',
        img: requireImg('share/hy_no_compress.png'),
        txt: '欢游好友',
        ext: 'tt_playmate_click',
    },
};
