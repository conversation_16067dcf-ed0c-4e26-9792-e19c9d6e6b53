/**
 * @description 目前有tt(tt语音),hy(欢游),mk(麦可),mj(谜境)4个马甲包配置
 * @description ios，android表示当前系统，tt其实是统一，但是hy跟mk需要
 * @param api: 后台服务地址
 * @param node: 活动的node服务地址
 * @param node_common: 公共的node服务地址（短链、口令、微信签名等）
 * @param web: 活动页面地址
 * @param activeAppOptions: 唤醒包的配置表
 * @param jsBridgeProtocol: 跟客户端对接的JSBridge协议头
 * @param jsBridgePreview: 跟客户端对接的JSBridge协议头+域名
 * @param website: 对应当前马甲包的官网
 * @param app: 版本页面地址
 * @param platformApp app马甲分享图
 */

import SVG from '../svg';

export default {
    app_name: 'TT语音',
    api_ios: {
        prod: '//api.52tt.com/',
        gray: '//api.52tt.com/',
        testing: '//testing-tt-web-tc.ttyuyin.com/',
        dev: '//testing-tt-web-tc.ttyuyin.com/',
    },
    api_android: {
        prod: '//api.52tt.com/',
        gray: '//api.52tt.com/',
        testing: '//testing-tt-web-tc.ttyuyin.com/',
        dev: '//testing-tt-web-tc.ttyuyin.com/',
    },
    node_ios: {
        prod: '//node-unify.52tt.com/activity-production/',
        gray: '//node-unify.52tt.com/activity-testing/',
        testing: '//testing-tt-web-tc.ttyuyin.com/activity-testing/',
        dev: '//testing-tt-web-tc.ttyuyin.com/activity-testing/',
    },
    node_android: {
        prod: '//node-unify.52tt.com/activity-production/',
        gray: '//node-unify.52tt.com/activity-testing/',
        testing: '//testing-tt-web-tc.ttyuyin.com/activity-testing/',
        dev: '//testing-tt-web-tc.ttyuyin.com/activity-testing/',
    },
    node_room_ios: {
        prod: '//node-room.52tt.com/common-production/',
        gray: '//node-room.52tt.com/common-production/',
        testing: '//testing-tt-web-tc.ttyuyin.com/common-testing/',
        dev: '//testing-tt-web-tc.ttyuyin.com/common-testing/',
    },
    node_room_android: {
        prod: '//node-room.52tt.com/common-production/',
        gray: '//node-room.52tt.com/common-production/',
        testing: '//testing-tt-web-tc.ttyuyin.com/common-testing/',
        dev: '//testing-tt-web-tc.ttyuyin.com/common-testing/',
    },
    node_common_ios: {
        prod: '//node-unify.52tt.com/common-production/',
        gray: '//node-unify.52tt.com/common-production/',
        testing: '//testing-tt-web-tc.ttyuyin.com/common-testing/',
        dev: '//testing-tt-web-tc.ttyuyin.com/common-testing/',
    },
    node_common_android: {
        prod: '//node-unify.52tt.com/common-production/',
        gray: '//node-unify.52tt.com/common-production/',
        testing: '//testing-tt-web-tc.ttyuyin.com/common-testing/',
        dev: '//testing-tt-web-tc.ttyuyin.com/common-testing/',
    },
    avatar_ios: {
        prod: 'https://avatar.52tt.com/v2/',
        gray: 'https://avatar.52tt.com/v2/',
        testing: 'https://testing-avatar.ttyuyin.com/v2/',
        dev: 'https://testing-avatar.ttyuyin.com/v2/',
    },
    avatar_android: {
        prod: 'https://avatar.52tt.com/v2/',
        gray: 'https://avatar.52tt.com/v2/',
        testing: 'https://testing-avatar.ttyuyin.com/v2/',
        dev: 'https://testing-avatar.ttyuyin.com/v2/',
    },
    web_ios: {
        dev: 'http://app.52tt.com/internal/frontend-web-activity-',
        internal: 'https://app.52tt.com/internal/frontend-web-activity-',
        testing: 'https://app.52tt.com/testing/frontend-web-activity-',
        gray: 'https://app.52tt.com/gray/frontend-web-activity-',
        prod: 'https://appcdn.52tt.com/web/frontend-web-activity-',
    },
    web_ios_share: {
        dev: 'http://app.i52tt.com/internal/frontend-web-activity-',
        internal: 'https://app.52tt.com/internal/frontend-web-activity-',
        testing: 'https://app.i52tt.com/testing/frontend-web-activity-',
        gray: 'https://app.i52tt.com/gray/frontend-web-activity-',
        prod: 'https://cdn.i52tt.com/web/frontend-web-activity-',
    },
    web_android: {
        dev: 'http://app.52tt.com/internal/frontend-web-activity-',
        internal: 'https://app.52tt.com/internal/frontend-web-activity-',
        testing: 'https://app.52tt.com/testing/frontend-web-activity-',
        gray: 'https://app.52tt.com/gray/frontend-web-activity-',
        prod: 'https://appcdn.52tt.com/web/frontend-web-activity-',
    },
    web_android_share: {
        dev: 'http://app.i52tt.com/internal/frontend-web-activity-',
        internal: 'https://app.i52tt.com/internal/frontend-web-activity-',
        testing: 'https://app.i52tt.com/testing/frontend-web-activity-',
        gray: 'https://app.i52tt.com/gray/frontend-web-activity-',
        prod: 'https://cdn.i52tt.com/web/frontend-web-activity-',
    },
    web_ios_host: {
        dev: 'http://app.52tt.com',
        internal: 'https://app.52tt.com',
        testing: 'https://app.52tt.com',
        gray: 'https://app.52tt.com',
        prod: 'https://appcdn.52tt.com',
    },
    web_ios_share_host: {
        dev: 'http://app.i52tt.com',
        internal: 'https://app.52tt.com',
        testing: 'https://app.i52tt.com',
        gray: 'https://app.i52tt.com',
        prod: 'https://cdn.i52tt.com',
    },
    web_android_host: {
        dev: 'http://app.52tt.com',
        internal: 'https://app.52tt.com',
        testing: 'https://app.52tt.com',
        gray: 'https://app.52tt.com',
        prod: 'https://appcdn.52tt.com',
    },
    web_android_share_host: {
        dev: 'http://app.i52tt.com',
        internal: 'https://app.i52tt.com',
        testing: 'https://app.i52tt.com',
        gray: 'https://app.i52tt.com',
        prod: 'https://cdn.i52tt.com',
    },
    app_ios: {
        dev: 'http://app.52tt.com/internal/frontend-web-assist-',
        internal: 'https://app.52tt.com/internal/frontend-web-assist-',
        testing: 'https://app.52tt.com/testing/frontend-web-assist-',
        gray: 'https://app.52tt.com/gray/frontend-web-assist-',
        prod: 'https://appcdn.52tt.com/web/frontend-web-assist-',
    },
    app_android: {
        dev: 'http://app.52tt.com/internal/frontend-web-assist-',
        internal: 'https://app.52tt.com/internal/frontend-web-assist-',
        testing: 'https://app.52tt.com/testing/frontend-web-assist-',
        gray: 'https://app.52tt.com/gray/frontend-web-assist-',
        prod: 'https://appcdn.52tt.com/web/frontend-web-assist-',
    },
    activeAppOptions: {
        androidLink: 'm://52tt.com/',
        universal: 'https://ul.52tt.com',
        appstore: 'https://itunes.apple.com/cn/app/id1012317813',
        fallback: 'https://a.app.qq.com/o/simple.jsp?pkgname=com.yiyou.ga',
        official: 'https://d.52tt.com/tt/official/tt.apk',
        wxOpenTagEl: '#wxBtn',
        createPsw: true,
        marketId: 0,
    },
    jsBridgeProtocol: 'tt://',
    jsBridgePreview: 'tt://m.52tt.com',
    website_ios: 'https://52tt.com',
    website_android: 'https://52tt.com',
    platformApp: {
        type: 'TT',
        svg: SVG.TT,
        txt: 'TT好友',
        ext: 'tt_playmate_click',
    },
};
