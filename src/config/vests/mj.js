/**
 * @description 目前有tt(tt语音),hy(欢游),mk(麦可),mj(谜境)4个马甲包配置
 * @description ios，android表示当前系统，tt其实是统一，但是hy跟mk需要
 * @param api: 后台服务地址
 * @param node: 活动的node服务地址
 * @param node_common: 公共的node服务地址（短链、口令、微信签名等）
 * @param web: 活动页面地址
 * @param activeAppOptions: 唤醒包的配置表
 * @param jsBridgeProtocol: 跟客户端对接的JSBridge协议头
 * @param jsBridgePreview: 跟客户端对接的JSBridge协议头+域名
 * @param website: 对应当前马甲包的官网
 * @param app: 版本页面地址
 * @param platformApp app马甲分享图
 */

export default {
    app_name: '谜境',
    api_ios: {
        prod: '//api.ukilive.com/',
        gray: '//api.ukilive.com/',
        testing: '//testing-tt-web-tc.ttyuyin.com/',
        dev: '//testing-tt-web-tc.ttyuyin.com/',
    },
    api_android: {
        prod: '//api.ukilive.com/',
        gray: '//api.ukilive.com/',
        testing: '//testing-tt-web-tc.ttyuyin.com/',
        dev: '//testing-tt-web-tc.ttyuyin.com/',
    },
    node_ios: {
        prod: '//node-unify.ukilive.com/activity-production/',
        gray: '//node-unify.ukilive.com/activity-testing/',
        testing: '//testing-tt-web-tc.ttyuyin.com/activity-testing/',
        dev: '//testing-tt-web-tc.ttyuyin.com/activity-testing/',
    },
    node_android: {
        prod: '//node-unify.ukilive.com/activity-production/',
        gray: '//node-unify.ukilive.com/activity-testing/',
        testing: '//testing-tt-web-tc.ttyuyin.com/activity-testing/',
        dev: '//testing-tt-web-tc.ttyuyin.com/activity-testing/',
    },
    node_room_ios: {
        prod: '//node-room.52tt.com/common-production/',
        gray: '//node-room.52tt.com/common-production/',
        testing: '//testing-tt-web-tc.ttyuyin.com/common-testing/',
        dev: '//testing-tt-web-tc.ttyuyin.com/common-testing/',
    },
    node_room_android: {
        prod: '//node-room.52tt.com/activity-production/',
        gray: '//node-room.52tt.com/common-production/',
        testing: '//testing-tt-web-tc.ttyuyin.com/common-testing/',
        dev: '//testing-tt-web-tc.ttyuyin.com/common-testing/',
    },
    node_common_ios: {
        prod: '//node-unify.ukilive.com/common-production/',
        gray: '//node-unify.ukilive.com/common-production/',
        testing: '//testing-tt-web-tc.ttyuyin.com/common-testing/',
        dev: '//testing-tt-web-tc.ttyuyin.com/common-testing/',
    },
    node_common_android: {
        prod: '//node-unify.ukilive.com/common-production/',
        gray: '//node-unify.ukilive.com/common-production/',
        testing: '//testing-tt-web-tc.ttyuyin.com/common-testing/',
        dev: '//testing-tt-web-tc.ttyuyin.com/common-testing/',
    },
    avatar_ios: {
        prod: 'https://avatar.ukilive.com/v2/',
        gray: 'https://avatar.ukilive.com/v2/',
        testing: 'https://testing-avatar.ttyuyin.com/v2/',
        dev: 'https://testing-avatar.ttyuyin.com/v2/',
    },
    avatar_android: {
        prod: 'https://avatar.ukilive.com/v2/',
        gray: 'https://avatar.ukilive.com/v2/',
        testing: 'https://testing-avatar.ttyuyin.com/v2/',
        dev: 'https://testing-avatar.ttyuyin.com/v2/',
    },
    web_ios: {
        dev: 'https://app.ukilive.com/testing/frontend-web-activity-',
        internal: 'https://app.ukilive.com/internal/frontend-web-activity-',
        testing: 'https://app.ukilive.com/testing/frontend-web-activity-',
        gray: 'https://app.ukilive.com/gray/frontend-web-activity-',
        prod: 'https://appcdn.ukilive.com/web/frontend-web-activity-',
    },
    web_ios_share: {
        dev: 'https://app.ukilive.com/testing/frontend-web-activity-',
        internal: 'https://app.ukilive.com/internal/frontend-web-activity-',
        testing: 'https://app.ukilive.com/testing/frontend-web-activity-',
        gray: 'https://app.ukilive.com/gray/frontend-web-activity-',
        prod: 'https://cdn.ukilive.com/web/frontend-web-activity-',
    },
    web_android: {
        dev: 'https://app.ukilive.com/testing/frontend-web-activity-',
        internal: 'https://app.ukilive.com/internal/frontend-web-activity-',
        testing: 'https://app.ukilive.com/testing/frontend-web-activity-',
        gray: 'https://app.ukilive.com/gray/frontend-web-activity-',
        prod: 'https://appcdn.ukilive.com/web/frontend-web-activity-',
    },
    web_android_share: {
        dev: 'https://app.ukilive.com/testing/frontend-web-activity-',
        internal: 'https://app.ukilive.com/internal/frontend-web-activity-',
        testing: 'https://app.ukilive.com/testing/frontend-web-activity-',
        gray: 'https://app.ukilive.com/gray/frontend-web-activity-',
        prod: 'https://cdn.ukilive.com/web/frontend-web-activity-',
    },
    web_ios_host: {
        dev: 'https://app.ukilive.com',
        internal: 'https://app.ukilive.com',
        testing: 'https://app.ukilive.com',
        gray: 'https://app.ukilive.com',
        prod: 'https://appcdn.ukilive.com',
    },
    web_ios_share_host: {
        dev: 'https://app.ukilive.com',
        internal: 'https://app.ukilive.com',
        testing: 'https://app.ukilive.com',
        gray: 'https://app.ukilive.com',
        prod: 'https://cdn.ukilive.com',
    },
    web_android_host: {
        dev: 'https://app.ukilive.com',
        internal: 'https://app.ukilive.com',
        testing: 'https://app.ukilive.com',
        gray: 'https://app.ukilive.com',
        prod: 'https://appcdn.ukilive.com',
    },
    web_android_share_host: {
        dev: 'https://app.ukilive.com',
        internal: 'https://app.ukilive.com',
        testing: 'https://app.ukilive.com',
        gray: 'https://app.ukilive.com',
        prod: 'https://cdn.ukilive.com',
    },
    app_ios: {
        dev: 'https://app.ukilive.com/testing/frontend-web-assist-',
        internal: 'https://app.ukilive.com/internal/frontend-web-assist-',
        testing: 'https://app.ukilive.com/testing/frontend-web-assist-',
        gray: 'https://app.ukilive.com/gray/frontend-web-assist-',
        prod: 'https://appcdn.ukilive.com/web/frontend-web-assist-',
    },
    app_android: {
        dev: 'https://app.ukilive.com/testing/frontend-web-assist-',
        internal: 'https://app.ukilive.com/internal/frontend-web-assist-',
        testing: 'https://app.ukilive.com/testing/frontend-web-assist-',
        gray: 'https://app.ukilive.com/gray/frontend-web-assist-',
        prod: 'https://appcdn.ukilive.com/web/frontend-web-assist-',
    },
    activeAppOptions: {
        androidLink: 'm://ukilive.com',
        universal: 'https://ul.ukilive.com',
        appstore: 'https://apps.apple.com/mx/app/id1641884007',
        fallback: 'https://a.app.qq.com/o/simple.jsp?pkgname=com.ukilive.wonderland',
        official: 'https://d.ukilive.com/mijing/offcial/mijing.apk',
        createPsw: true,
        marketId: 6,
    },
    jsBridgeProtocol: 'tmj://',
    jsBridgePreview: 'tmj://m.ukilive.com',
    website_ios: 'https://ukilive.com',
    website_android: 'https://ukilive.com',
    platformApp: {
        type: 'TT',
        img: requireImg('share/mj_no_compress.png'),
        txt: '谜境好友',
        ext: 'tt_playmate_click',
    },
};
