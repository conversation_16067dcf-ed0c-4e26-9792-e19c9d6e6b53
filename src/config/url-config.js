import tt from './vests/tt';
import hy from './vests/hy';
import mk from './vests/mk';
import mj from './vests/mj';

// 导出当前马甲的配置信息
const getUrlConfig = () => {
    switch (MARKET_ID) {
        case MARKET_ID_MAP.TT:
            return tt;
        case MARKET_ID_MAP.HY:
            return hy;
        case MARKET_ID_MAP.MK:
            return mk;
        case MARKET_ID_MAP.MJ:
            return mj;

        default:
            return tt;
    }
};

export const urlConfig = getUrlConfig();
