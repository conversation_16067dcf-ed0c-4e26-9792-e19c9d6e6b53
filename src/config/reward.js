export const rewardList = [
    {
        resource_id: 'A1',
        name: '水晶鞋',
        type: 'gift',
        mark: '礼物',
        unit: '个',
        price: 5000,
        special_type: '礼物架豆豆礼物',
        image: 'A1.png',
    },
    {
        resource_id: 'A2',
        name: '小丑',
        type: 'gift',
        mark: '礼物',
        unit: '个',
        price: 10,
        special_type: '',
        image: 'A2.png',
    },
    {
        resource_id: 'A3',
        type: 'base_flow_channel',
        mark: '基础流量',
        unit: '天',
        special_type: 'S级',
        image: 'A3.png',
    },
    {
        resource_id: 'A4',
        type: 'base_flow_channel',
        mark: '基础流量',
        unit: '天',
        special_type: 'A级',
        image: 'A4.png',
    },
    {
        resource_id: 'A5',
        name: '元旦背景A',
        type: 'other',
        mark: '房间背景',
        unit: '天',
        special_type: '',
        image: 'A5.png',
    },
    {
        resource_id: 'A6',
        name: '0.1元lv2冰沙',
        type: 'headwear',
        mark: '麦位框',
        unit: '天',
        special_type: '',
        image: 'A6.png',
    },
    {
        resource_id: 'A7',
        name: '甜蜜花海',
        type: 'package',
        mark: '包裹',
        unit: '个',
        price: 50000,
        special_type: '豆豆礼物',
        image: 'A7.png',
    },
    {
        resource_id: 'A8',
        name: '梦生花',
        type: 'package',
        mark: '包裹',
        unit: '个',
        price: 10000,
        special_type: '豆豆礼物',
        image: 'A8.png',
    },
    {
        resource_id: 'A9',
        name: '营收100男女',
        type: 'package',
        mark: '包裹',
        unit: '个',
        price: 10000,
        special_type: '豆豆礼物',
        image: 'A9.png',
    },
    {
        resource_id: 'A10',
        name: '甜品摊位100',
        type: 'package',
        mark: '包裹',
        unit: '个',
        price: 10000,
        special_type: '豆豆礼物',
        image: 'A10.png',
    },
    {
        resource_id: 'A11',
        name: '山河探游录当红赛区TOP1达人',
        type: 'official_cert',
        mark: '大V认证',
        unit: '天',
        special_type: '粉v认证',
        image: 'A11.png',
    },
    {
        resource_id: 'A12',
        name: '山河探游录当红赛区TOP2达人',
        type: 'official_cert',
        mark: '大V认证',
        unit: '天',
        special_type: '粉v认证',
        image: 'A12.png',
    },
    {
        resource_id: 'A13',
        name: '山河探游录当红赛区TOP3达人',
        type: 'official_cert',
        mark: '大V认证',
        unit: '天',
        special_type: '粉v认证',
        image: 'A13.png',
    },
    {
        resource_id: 'A14',
        name: '山河探游录当红赛区十强达人',
        type: 'official_cert',
        mark: '大V认证',
        unit: '天',
        special_type: '粉v认证',
        image: 'A14.png',
    },
    {
        resource_id: 'A15',
        name: '山河探游录新锐赛区TOP1达人',
        type: 'official_cert',
        mark: '大V认证',
        unit: '天',
        special_type: '粉v认证',
        image: 'A15.png',
    },
    {
        resource_id: 'A16',
        name: '豹豹lv3',
        type: 'mount',
        mark: '坐骑',
        unit: '天',
        special_type: '',
        image: 'A16.png',
    },
    {
        resource_id: 'A17',
        type: 'flow_card',
        mark: '流量卡',
        unit: '张',
        special_type: '普通流量卡',
        image: 'A17.png',
    },
    {
        resource_id: 'A18',
        name: '0.1元lv2葡萄',
        type: 'headwear',
        mark: '麦位框',
        unit: '天',
        special_type: '',
        image: 'A18.png',
    },
    {
        resource_id: 'A19',
        name: '千灯月夜',
        type: 'fans_plate',
        mark: '粉丝团铭牌',
        unit: '天',
        special_type: '',
        image: 'A19.png',
    },
    {
        resource_id: 'A20',
        type: 'base_flow_channel',
        mark: '基础流量',
        unit: '天',
        special_type: 'B级',
        image: 'A20.png',
    },
    {
        resource_id: 'A21',
        name: '测试2',
        type: 'official_cert',
        mark: '大V认证',
        unit: '天',
        special_type: '',
        image: 'A21.png',
    },
    {
        resource_id: 'A22',
        name: '环游领航员',
        type: 'official_cert',
        mark: '大V认证',
        unit: '天',
        special_type: '',
        image: 'A22.png',
    },
    {
        resource_id: 'A23',
        name: '星级幸运儿',
        type: 'official_cert',
        mark: '大V认证',
        unit: '天',
        special_type: '',
        image: 'A23.png',
    },
    {
        resource_id: 'A24',
        name: '0.1元lv2咖啡',
        type: 'headwear',
        mark: '麦位框',
        unit: '天',
        special_type: '',
        image: 'A24.png',
    },
    {
        resource_id: 'A25',
        name: '完美人格',
        type: 'user_plate',
        mark: '个人铭牌',
        unit: '天',
        special_type: '',
        image: 'A25.png',
    },
    {
        resource_id: 'A26',
        name: '小丑',
        type: 'package',
        mark: '包裹',
        unit: '个',
        price: 10,
        special_type: '红钻礼物',
        image: 'A26.png',
    },
];

export const rewardMap = {
    A1: {
        resource_id: 'A1',
        name: '水晶鞋',
        type: 'gift',
        mark: '礼物',
        unit: '个',
        price: 5000,
        special_type: '礼物架豆豆礼物',
        image: 'A1.png',
    },
    A2: {
        resource_id: 'A2',
        name: '小丑',
        type: 'gift',
        mark: '礼物',
        unit: '个',
        price: 10,
        special_type: '',
        image: 'A2.png',
    },
    A3: {
        resource_id: 'A3',
        type: 'base_flow_channel',
        mark: '基础流量',
        unit: '天',
        special_type: 'S级',
        image: 'A3.png',
    },
    A4: {
        resource_id: 'A4',
        type: 'base_flow_channel',
        mark: '基础流量',
        unit: '天',
        special_type: 'A级',
        image: 'A4.png',
    },
    A5: {
        resource_id: 'A5',
        name: '元旦背景A',
        type: 'other',
        mark: '房间背景',
        unit: '天',
        special_type: '',
        image: 'A5.png',
    },
    A6: {
        resource_id: 'A6',
        name: '0.1元lv2冰沙',
        type: 'headwear',
        mark: '麦位框',
        unit: '天',
        special_type: '',
        image: 'A6.png',
    },
    A7: {
        resource_id: 'A7',
        name: '甜蜜花海',
        type: 'package',
        mark: '包裹',
        unit: '个',
        price: 50000,
        special_type: '豆豆礼物',
        image: 'A7.png',
    },
    A8: {
        resource_id: 'A8',
        name: '梦生花',
        type: 'package',
        mark: '包裹',
        unit: '个',
        price: 10000,
        special_type: '豆豆礼物',
        image: 'A8.png',
    },
    A9: {
        resource_id: 'A9',
        name: '营收100男女',
        type: 'package',
        mark: '包裹',
        unit: '个',
        price: 10000,
        special_type: '豆豆礼物',
        image: 'A9.png',
    },
    A10: {
        resource_id: 'A10',
        name: '甜品摊位100',
        type: 'package',
        mark: '包裹',
        unit: '个',
        price: 10000,
        special_type: '豆豆礼物',
        image: 'A10.png',
    },
    A11: {
        resource_id: 'A11',
        name: '山河探游录当红赛区TOP1达人',
        type: 'official_cert',
        mark: '大V认证',
        unit: '天',
        special_type: '粉v认证',
        image: 'A11.png',
    },
    A12: {
        resource_id: 'A12',
        name: '山河探游录当红赛区TOP2达人',
        type: 'official_cert',
        mark: '大V认证',
        unit: '天',
        special_type: '粉v认证',
        image: 'A12.png',
    },
    A13: {
        resource_id: 'A13',
        name: '山河探游录当红赛区TOP3达人',
        type: 'official_cert',
        mark: '大V认证',
        unit: '天',
        special_type: '粉v认证',
        image: 'A13.png',
    },
    A14: {
        resource_id: 'A14',
        name: '山河探游录当红赛区十强达人',
        type: 'official_cert',
        mark: '大V认证',
        unit: '天',
        special_type: '粉v认证',
        image: 'A14.png',
    },
    A15: {
        resource_id: 'A15',
        name: '山河探游录新锐赛区TOP1达人',
        type: 'official_cert',
        mark: '大V认证',
        unit: '天',
        special_type: '粉v认证',
        image: 'A15.png',
    },
    A16: {
        resource_id: 'A16',
        name: '豹豹lv3',
        type: 'mount',
        mark: '坐骑',
        unit: '天',
        special_type: '',
        image: 'A16.png',
    },
    A17: {
        resource_id: 'A17',
        type: 'flow_card',
        mark: '流量卡',
        unit: '张',
        special_type: '普通流量卡',
        image: 'A17.png',
    },
    A18: {
        resource_id: 'A18',
        name: '0.1元lv2葡萄',
        type: 'headwear',
        mark: '麦位框',
        unit: '天',
        special_type: '',
        image: 'A18.png',
    },
    A19: {
        resource_id: 'A19',
        name: '千灯月夜',
        type: 'fans_plate',
        mark: '粉丝团铭牌',
        unit: '天',
        special_type: '',
        image: 'A19.png',
    },
    A20: {
        resource_id: 'A20',
        type: 'base_flow_channel',
        mark: '基础流量',
        unit: '天',
        special_type: 'B级',
        image: 'A20.png',
    },
    A21: {
        resource_id: 'A21',
        name: '测试2',
        type: 'official_cert',
        mark: '大V认证',
        unit: '天',
        special_type: '',
        image: 'A21.png',
    },
    A22: {
        resource_id: 'A22',
        name: '环游领航员',
        type: 'official_cert',
        mark: '大V认证',
        unit: '天',
        special_type: '',
        image: 'A22.png',
    },
    A23: {
        resource_id: 'A23',
        name: '星级幸运儿',
        type: 'official_cert',
        mark: '大V认证',
        unit: '天',
        special_type: '',
        image: 'A23.png',
    },
    A24: {
        resource_id: 'A24',
        name: '0.1元lv2咖啡',
        type: 'headwear',
        mark: '麦位框',
        unit: '天',
        special_type: '',
        image: 'A24.png',
    },
    A25: {
        resource_id: 'A25',
        name: '完美人格',
        type: 'user_plate',
        mark: '个人铭牌',
        unit: '天',
        special_type: '',
        image: 'A25.png',
    },
    A26: {
        resource_id: 'A26',
        name: '小丑',
        type: 'package',
        mark: '包裹',
        unit: '个',
        price: 10,
        special_type: '红钻礼物',
        image: 'A26.png',
    },
};
