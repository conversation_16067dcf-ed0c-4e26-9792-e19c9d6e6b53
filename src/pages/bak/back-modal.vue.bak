<template>
    <modal-container
        v-model:show="isShow"
        :close-on-click-overlay="false">
        <div class="back-modal">
            <van-button @click="finishActivity">退出</van-button>
            <van-button @click="nextFn(false)">留下</van-button>
        </div>
    </modal-container>
</template>

<script setup>
import { finishActivity } from '@/utils/jsbridge';

const isShow = ref(false);
const nextFn = ref(() => null);

useEventBus('back-modal').on(({ show = true, next }) => {
    isShow.value = show;
    nextFn.value = next;
});
</script>

<style lang="less" scoped>
.back-modal {
    width: 250px;
    height: 250px;
    border-radius: 15px;
    background-color: #fff;
}
</style>
