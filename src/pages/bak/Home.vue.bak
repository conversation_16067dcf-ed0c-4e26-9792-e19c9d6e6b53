<route lang="json">
{
    "name": "Home",
    "path": "/:pathMatch(.*)*",
    "meta": {
        "title": "首页"
    }
}
</route>

<template>
    <page-container>
        <div class="home"></div>
    </page-container>
</template>

<script setup name="Home">
const router = useRouter();

onBeforeMount(async () => {
    router.push({ path: '/home-target', query: { _no_trans: 1 } });
});
</script>

<style lang="less" scoped>
.home {
    min-height: 100%;
}
</style>
