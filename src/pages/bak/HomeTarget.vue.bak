<route lang="json">
{
    "name": "HomeTarget",
    "path": "/home-target",
    "meta": {
        "title": "首页"
    }
}
</route>

<template>
    <page-container>
        <div class="home-target">
            首页
            <img
                class="block h-100 w-100 bg-coolGray"
                :src="requireImg('default_avatar_no_compress.png')"
                @click="toRule" />
            isEnd:{{ isEnd }}
            <br />
            market_id:{{ MARKET_ID }}
            <br />
            是否TT:{{ MARKET_ID === MARKET_ID_MAP.TT }}
            <br />
            initData:{{ initData }}
            <br />
            导入public目录下静态资源链接，lottie/GE动效json可用：{{ requirePublic('/apm-sdk.js') }}
        </div>
    </page-container>
</template>

<script setup name="HomeTarget">
import useInitStore from '@/stores/modules/use-init-store';
import { finishActivity } from '@/utils/jsbridge';

const router = useRouter();
const initStore = useInitStore();

const toRule = () => {
    router.push({ path: '/rule', query: { _no_trans: 1 } });
    // 打开分享弹窗
    // useEventBus('share-modal').emit({ show: true });
};

onMounted(async () => {
    const toast = showLoading();
    await initStore.init();
    toast.close();
});

onBeforeRouteLeave((to, from, next) => {
    if (to.name === 'Home') {
        // 一些判断逻辑，做挽留
        if (true) {
            setTimeout(() => {
                // 将next方法传入modal自行判断，如果是退出页面直接调用finishActivity，如果是留下则调用next(false)
                useEventBus('back-modal').emit({ show: true, next });
            }, 300);
        }
        else {
            finishActivity();
        }
    }
    else {
        next();
    }
});
</script>

<style lang="less" scoped>
.home-target {
    min-height: 100%;
    background-color: skyblue;
}
</style>
