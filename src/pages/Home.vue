<route lang="json">
{
    "name": "Home",
    "path": "/:pathMatch(.*)*",
    "meta": {
        "title": "首页"
    }
}
</route>

<template>
    <page-container>
        <div class="home">
            <img
                class="rule-btn"
                :src="requireImg('<EMAIL>')"
                @click="jumpToLink('/web/beluga-activity-684fde0609f2573bba8cef7e/index.html');" />
            <img
                class="challenge-btn"
                :src="requireImg('<EMAIL>')"
                @click="useEventBus('daily-challenge-popup').emit({ show: true })" />
            <div class="banner"></div>
            <div class="tab-box">
                <ActivityTabs class="z-1" />
            </div>
            <template v-if="serverTime">
                <Tab1 v-if="activityTabsStore?.activeTabId === TAB_TYPES.TAB_1" />
                <Tab2 v-if="activityTabsStore?.activeTabId === TAB_TYPES.TAB_2" />
                <Tab3 v-if="activityTabsStore?.activeTabId === TAB_TYPES.TAB_3" />
                <daily-challenge-popup />
            </template>
        </div>
    </page-container>
</template>

<script setup name="Home">
import useInitStore from '@/stores/modules/use-init-store';
import useActivityTabsStore, { TAB_TYPES } from '@/stores/modules/use-activity-tabs-store';

const activityTabsStore = useActivityTabsStore();
const router = useRouter();
const initStore = useInitStore();
const { serverTime } = storeToRefs(useInitStore());
const toRule = () => {
    router.push('/rule');
    // 打开分享弹窗
    useEventBus('share-modal').emit({ show: true });
};

onMounted(async () => {
    const toast = showLoading();
    await initStore.init();
    toast.close();
});
</script>

<style lang="less" scoped>
.home {
    min-height: 100%;
    background: #151621;
    position: relative;
    .banner {
        width: 100%;
        height: 197px;
        .full-bg('@/assets/img/<EMAIL>');
    }
    .rule-btn {
        width: 28px;
        height: 16px;
        position: absolute;
        top: 31px;
        right: 7px;
        z-index: 2;
    }
    .challenge-btn {
        width: 58px;
        height: 53px;
        position: absolute;
        top: 60px;
        right: 7px;
        z-index: 2;
    }
    .tab-box {
        position: fixed;
        bottom: -1px;
        left: 0;
        width: 100%;
        height: 80px;
        background: #23243d;
        padding-top: 15px;
        z-index: 3;
    }
}
</style>
