import { createApp } from 'vue';
import mockjs from 'mockjs?url';
import App from './App.vue';
import pinia from './stores';
import { loadScriptSync } from '@/utils';
// import 'virtual:uno.css';
import './styles/app.less';
// Vant 桌面端适配
// import '@vant/touch-emulator';

const main = async () => {
    try {
        const { mock } = myWebview.params;
        if (mock)
            await loadScriptSync(mockjs);
    }
    catch {}

    const app = createApp(App);
    app.use(pinia);
    app.mount('#app');
};

main();
