// src/stores/modules/use-tour-store.js
import { defineStore } from 'pinia';
import { followAnchor, getTourList, testApi, unfollowAnchor } from '@/api';

/**
 * 官频巡演状态管理
 */
const useTourStore = defineStore('tour', () => {
    const state = reactive({
        // 巡演场次列表
        schedules: [],
        // 当前场次ID
        currentScheduleId: null,
        // 当前表演主播ID
        currentAnchorId: null,
        // 加载状态
        loading: false,
        // 错误信息
        error: null,
    });

    // 当前场次
    const currentSchedule = computed(() => {
        return state.schedules.find(schedule => schedule.scheduleId === state.currentScheduleId) || null;
    });

    // 当前场次状态 0-未开始, 1-进行中, 2-已结束
    const currentScheduleStatus = computed(() => {
        return currentSchedule.value?.status || 0;
    });

    // 当前表演主播
    const currentAnchor = computed(() => {
        if (!currentSchedule.value || !state.currentAnchorId)
            return null;
        return currentSchedule.value.anchors.find(anchor => anchor.anchorId === state.currentAnchorId) || null;
    });

    // 是否有主播报名
    const hasAnchors = computed(() => {
        return currentSchedule.value?.anchors?.length > 0;
    });

    // 下一次开启时间（用于倒计时）
    const nextStartTime = computed(() => {
        if (!currentSchedule.value)
            return 0;

        const now = serverTime.value;
        const schedule = currentSchedule.value;

        // 如果当前场次未开始，返回场次开始时间
        if (schedule.status === 0) {
            return schedule.startTime;
        }

        // 如果当前场次进行中或已结束，查找下一个场次
        const nextSchedule = state.schedules.find(s => s.startTime > now);
        return nextSchedule?.startTime || 0;
    });

    // 倒计时剩余时间（秒）
    const countdownTime = computed(() => {
        if (!nextStartTime.value)
            return 0;
        const remaining = nextStartTime.value - serverTime.value;
        return Math.max(0, remaining);
    });

    // 预约
    async function handleReserve() {
        try {
            const [{ code }] = await testApi();
            if (code === 0) {
                showToast('预约成功');
            }
        }
        catch (error) {
            console.error('预约失败:', error);
            showToast('网络错误，请稍后重试');
        }
    }
    /**
     * 获取巡演列表
     */
    async function fetchTourList() {
        try {
            state.loading = true;
            state.error = null;

            const [{ code, data }] = await getTourList();

            if (code === 0) {
                state.schedules = data.schedules || [];
                state.currentScheduleId = data.currentScheduleId || null;
                state.currentAnchorId = data.currentAnchorId || null;
            }
            else {
                state.error = '获取巡演数据失败';
            }
        }
        catch (error) {
            console.error('获取巡演列表失败:', error);
            state.error = '网络错误，请稍后重试';
        }
        finally {
            state.loading = false;
        }
    }

    /**
     * 关注主播
     */
    async function handleFollowAnchor(anchorId) {
        try {
            const [{ code }] = await followAnchor({ uid: initData.value.userInfo?.uid, anchorId });

            if (code === 0) {
                // 更新本地状态
                const schedule = state.schedules.find(s => s.scheduleId === state.currentScheduleId);
                if (schedule) {
                    const anchor = schedule.anchors.find(a => a.anchorId === anchorId);
                    if (anchor) {
                        anchor.isFollowed = true;
                    }
                }
                showToast('关注成功');
                return true;
            }
            else {
                showToast('关注失败，请稍后重试');
                return false;
            }
        }
        catch (error) {
            console.error('关注主播失败:', error);
            showToast('网络错误，请稍后重试');
            return false;
        }
    }

    /**
     * 取消关注主播
     */
    async function handleUnfollowAnchor(anchorId) {
        try {
            const [{ code }] = await unfollowAnchor({ uid: initData.value.userInfo?.uid, anchorId });

            if (code === 0) {
                // 更新本地状态
                const schedule = state.schedules.find(s => s.scheduleId === state.currentScheduleId);
                if (schedule) {
                    const anchor = schedule.anchors.find(a => a.anchorId === anchorId);
                    if (anchor) {
                        anchor.isFollowed = false;
                    }
                }
                showToast('已取消关注');
                return true;
            }
            else {
                showToast('取消关注失败，请稍后重试');
                return false;
            }
        }
        catch (error) {
            console.error('取消关注主播失败:', error);
            showToast('网络错误，请稍后重试');
            return false;
        }
    }

    /**
     * 跳转到主播直播间
     */
    function goToAnchorRoom(roomId) {
        if (!roomId) {
            showToast('直播间信息错误');
            return;
        }

        // 使用项目中的跳转方法
        toRoom(roomId);
    }

    /**
     * 重置状态
     */
    function reset() {
        state.schedules = [];
        state.currentScheduleId = null;
        state.currentAnchorId = null;
        state.loading = false;
        state.error = null;
    }

    return {
        ...toRefs(state),
        // computed
        currentSchedule,
        currentScheduleStatus,
        currentAnchor,
        hasAnchors,
        nextStartTime,
        countdownTime,
        // methods
        fetchTourList,
        handleFollowAnchor,
        handleUnfollowAnchor,
        goToAnchorRoom,
        reset,
        handleReserve,
    };
});

export default useTourStore;
