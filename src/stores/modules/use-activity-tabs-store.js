import { defineStore } from 'pinia';
import { parseUrlQuery } from '@/auto-imports/my-webview';
import { track } from '@/utils/jsbridge';

// tab类型
export const TAB_TYPES = {
    TAB_1: 1,
    TAB_2: 2,
    TAB_3: 3,
};

const query = parseUrlQuery();

const trackTabChange = (name = '') => {
    // console.log('trackTabChange', JSON.stringify({ tab_name: name }));
    track(
        {
            page_id: 'activity_page',
            event_id: 'tab_click',
            general_param_1: JSON.stringify({ tab_name: name }),
        },
        'click',
    );
};

/**
 * 活动tabs管理
 */
const useActivityTabsStore = defineStore('activity-tabs', () => {
    // tab列表
    const tabList = ref([
        {
            id: TAB_TYPES.TAB_1,
            name: '舞伴邀约',
            activeIcon: requireImg('tab/<EMAIL>'),
            inactiveIcon: requireImg('tab/<EMAIL>'),
        },
        {
            id: TAB_TYPES.TAB_2,
            name: '与光共舞',
            activeIcon: requireImg('tab/<EMAIL>'),
            inactiveIcon: requireImg('tab/<EMAIL>'),
        },
        {
            id: TAB_TYPES.TAB_3,
            name: '舞神降临',
            activeIcon: requireImg('tab/<EMAIL>'),
            inactiveIcon: requireImg('tab/<EMAIL>'),
        },
    ]);

    // 当前选中的tab
    const activeTabId = ref(TAB_TYPES.PARTY_KING);

    // 当前选中的tab数据
    const activeTab = computed(() => {
        return tabList.value.find(tab => tab.id === activeTabId.value);
    });

    /**
     * 切换tab
     * @param {string} tabId tab的ID
     */
    function switchTab(tabId) {
        if (Object.values(TAB_TYPES).includes(tabId)) {
            activeTabId.value = tabId;
            // console.log('sss', tabId);
            trackTabChange(activeTab.value.name);
        }
    }

    /**
     * 重置为默认tab
     */
    function resetTab() {
        activeTabId.value = TAB_TYPES.TAB_1;
    }

    function initTab() {
        if (query?.tab) {
            switchTab(query.tab);
            return;
        }
        resetTab();
    }

    initTab();

    return {
        TAB_TYPES,
        tabList,
        activeTabId,
        activeTab,
        switchTab,
        resetTab,
    };
});

export default useActivityTabsStore;
