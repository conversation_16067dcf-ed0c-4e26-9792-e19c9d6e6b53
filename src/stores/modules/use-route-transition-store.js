import { defineStore } from 'pinia';
import { ROUTE_TRANSITION, ROUTE_TRANSITION_TYPE } from '@/router/route-transition';

const useRouteTransitionStore = defineStore('route-transition', () => {
    // 过渡动画类名
    const routeTransitionName = ref('');
    const routeTransitionType = ref(ROUTE_TRANSITION_TYPE.FADE_IN);

    const setDirection = (direction) => {
        if (direction === 'none') {
            routeTransitionName.value = '';
            return;
        }
        routeTransitionName.value = ROUTE_TRANSITION[routeTransitionType.value][direction];
    };

    const setTransiton = (type) => {
        routeTransitionType.value = type;
    };

    return {
        routeTransitionName,
        routeTransitionType,
        setDirection,
        setTransiton,
    };
});

export default useRouteTransitionStore;
