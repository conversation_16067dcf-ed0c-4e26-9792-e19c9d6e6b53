export default function useVisible(visibleCallback, hiddenCallback) {
    onMounted(() => {
        window.addEventListener('visibilitychange', handleVisibilityChange);
    });

    onBeforeUnmount(() => {
        window.removeEventListener('visibilitychange', handleVisibilityChange);
    });

    function handleVisibilityChange() {
        if (document.visibilityState === 'visible') {
            visibleCallback?.();
        }
        else {
            hiddenCallback?.();
        }
    }
}
