import ResizeObserverPolyfill from 'resize-observer-polyfill';

export default function useResizeObserver() {
    const $observeTarget = ref(null);
    const size = ref({ width: 0, height: 0, widthVW: 0, heightVW: 0 });
    let observer = null;
    let lastObserved = null;

    const getResizeObserver = () => {
        if (typeof window !== 'undefined') {
            return window.ResizeObserver || ResizeObserverPolyfill;
        }
        else {
            return ResizeObserverPolyfill;
        }
    };

    const updateSize = (el) => {
        const width = el.offsetWidth;
        const height = el.offsetHeight;
        size.value = {
            width,
            height,
            widthVW: width / 375 * 100,
            heightVW: height / 375 * 100,
        };
    };

    const observe = (el) => {
        const ResizeObserverClass = getResizeObserver();
        if (!ResizeObserverClass) {
            return;
        }
        if (!observer) {
            observer = new ResizeObserverClass((entries) => {
                for (const entry of entries) {
                    updateSize(entry.target);
                }
            });
        }
        if (el) {
            updateSize(el);
            observer.observe(el);
            lastObserved = el;
        }
    };

    const unobserve = (el) => {
        if (observer && el) {
            observer.unobserve(el);
            if (lastObserved === el) {
                lastObserved = null;
            }
        }
    };

    onMounted(() => {
        if ($observeTarget.value) {
            observe($observeTarget.value);
        }
    });

    watch(
        () => $observeTarget.value,
        (newVal, oldVal) => {
            if (oldVal) {
                unobserve(oldVal);
            }
            if (newVal) {
                observe(newVal);
            }
        },
        { immediate: true },
    );

    onBeforeUnmount(() => {
        if (lastObserved) {
            unobserve(lastObserved);
        }
        observer = null;
    });

    return { $observeTarget, size };
}
