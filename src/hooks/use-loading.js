export default function useLoading(loading, text) {
    const $loading = ref(null);

    watch(
        () => loading.value,
        (val) => {
            if (val) {
                $loading.value = showLoading(text || undefined);
            }
            else {
                clear();
            }
        },
    );

    onBeforeUnmount(() => {
        clear();
    });

    function clear() {
        if ($loading.value) {
            $loading.value.close();
            $loading.value = null;
        }
    }

    return {
        $loading,
    };
}
