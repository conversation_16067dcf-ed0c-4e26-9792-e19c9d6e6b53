<template>
    <modal-container
        v-model:show="isShow"
        :close-on-click-overlay="true">
        <div class="share-modal">
            <div class="share-title">分享到</div>
            <div class="share-wrap">
                <div
                    v-for="item of platformMap"
                    :key="item.type"
                    class="share-item"
                    @click="handleShare(item)">
                    <img
                        v-if="item.img"
                        class="share-item-img"
                        :src="item.img" />
                    <div
                        v-else
                        :style="{ width: '46px', height: '46px', transform: `scale(${calcPx(1)})` }"
                        class="share-item-svg"
                        v-html="item.svg"></div>
                    <div class="txt">
                        {{ item.txt }}
                    </div>
                </div>
            </div>
        </div>
    </modal-container>
</template>

<script setup>
import { checkInstallApp } from '@/utils/jsbridge';
import SVG from '@/config/svg';
import { urlConfig } from '@/config/url-config';
import { afterVersion } from '@/utils';

const isShow = ref(false);
const basePlatformMap = ref([
    {
        type: 'QQ',
        svg: SVG.QQ,
        txt: 'QQ好友',
        ext: 'qq_friend_click',
    },
    {
        type: 'QZone',
        svg: SVG.QZone,
        txt: 'QQ空间',
        ext: 'qq_space_click',
    },
    {
        type: 'Wechat',
        svg: SVG.Wechat,
        txt: '微信好友',
        ext: 'wechat_friend_click',
    },
    {
        type: 'WechatMoments',
        svg: SVG.WechatMoments,
        txt: '微信朋友圈',
        ext: 'wechat_friend_circle_click',
    },
]);

const platformMap = computed(() => [urlConfig.platformApp, ...basePlatformMap.value]);

const handleShare = async ({ type }) => {
    isShow.value = false;
    try {
        const targetShareData = {
            ttShareMsgType: 0, // 1图片，其他（图文）0 默认
            title: '为你推荐我写的小说',
            content: '来TT为我助力你也能写全本小说',
            url: `${window.location.protocol}//${window.location.host}${window.location.pathname}`, // 图文调整链接
            imageUrl:
                'https://obs-cdn.52tt.com/tt/fe-moss/web/reborn-ai-novel-2024/20240417180250_22395655.png', // 图文里的缩略图url
            imagePath: '', // 纯图片类型（仅支持手机本地地址）
            label: '', // 入口标签名
            musicUrl: '',
            share_type: type,
        };
        if (myWebview.isIOS() && !afterVersion('3.6.2')) {
            showToast('当前客户端版本不支持该操作，请升级最新版本');
            return;
        }
        // 安卓先检查是否安装APP
        else {
            if (['Wechat', 'WechatMoments'].includes(type)) {
                if (!checkInstallApp('Wechat')) {
                    showToast('分享失败，未安装微信');
                    return;
                }
            }
            if (['QQ', 'QZone'].includes(type)) {
                if (!checkInstallApp('QQ')) {
                    showToast('分享失败，未安装QQ');
                    return;
                }
            }
        }

        TTJSBridge.invoke('ui', 'thirdPlatformShare', JSON.stringify(targetShareData)); // 新接口
    }
    catch {}
};

useEventBus('share-modal').on(({ show = true }) => {
    isShow.value = show;
});
</script>

<style lang="less" scoped>
.share-modal {
    width: 269px;
    height: 243px;
    border-radius: 15px;
    background-color: #fff;
    padding-top: 22px;
    .share-title {
        text-align: center;
        font-size: 18px;
        text-align: center;
        color: #333333;
        line-height: 20px;
        font-weight: bold;
    }
    .share-wrap {
        margin-top: 8px;
        display: flex;
        flex-wrap: wrap;
        padding: 0 21.5px;
        .share-item {
            width: 75px;
            text-align: center;
            margin-top: 12px;
            .share-item-svg {
                margin: 0 auto;
            }
            .share-item-img {
                width: 46px;
                height: 46px;
                padding: 11px;
                margin: 0 auto;
            }
            .txt {
                font-size: 12px;
                text-align: center;
                color: #333333;
                line-height: 16px;
            }
        }
    }
}
</style>
