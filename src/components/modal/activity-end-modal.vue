<template>
    <modal-container
        v-model:show="isShow"
        :close-on-click-overlay="false">
        <div class="activity-end-modal">
            <p class="text">活动已下线</p>
            <p class="tips">去活动中心看看其他活动吧~</p>
        </div>
    </modal-container>
</template>

<script setup>
const isShow = ref(false);

useEventBus('activity-end-modal').on(({ show = true }) => {
    isShow.value = show;
});
</script>

<style lang="less" scoped>
.activity-end-modal {
    width: 270px;
    padding: 45px 0;
    background-color: #fff;
    border-radius: 10px;
    font-family: PingFangSC-Medium;
    text-align: center;
    .text {
        font-size: 16px;
        color: #62a6ff;
    }
    .tips {
        margin-top: 8px;
        font-size: 13px;
        color: #bbb;
    }
}
</style>
