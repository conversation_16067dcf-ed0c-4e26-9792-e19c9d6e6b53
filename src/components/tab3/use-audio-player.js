import { defineStore } from 'pinia';
import { computed, onBeforeUnmount, onMounted, ref } from 'vue';
import { getHeartbeatChallengeData, setScore } from '@/api/draw-apis';
// import { track } from '@/utils/webview-init';

const useAudioPlayer = defineStore('audio-player', () => {
    const secondsSum = ref(0); // 暴露 secondsSum 为响应式数据
    const duration = ref(0);
    const notPlay = ref(false);
    class AudioPlayer {
        constructor() {
            this.audio = new Audio();
            this.currentSrc = '';
            this.secondsSum = 0; // 当前歌曲累计播放时长
            this.countTimer = null; // 计时器
        }

        // 设置音频源并播放
        setSrcAndPlay(src, needPlay = true) {
            if (this.currentSrc !== src) {
                this.audio.src = src;
                this.currentSrc = src;
                this.secondsSum = 0;
                // 同步更新响应式数据
                secondsSum.value = 0;
            // 不在这里开始计时，等到实际播放时再开始
            }
            this.audio.loop = true;
            if (needPlay) {
                this.replay();
            }
        }

        countSeconds() {
            this.clearCountTimer();
            if (this.secondsSum >= 30) {
                return;
            }
            this.countTimer = setInterval(() => {
            // 只有在音频实际播放时才计时

                if (!this.audio.paused && !this.audio.ended) {
                    this.secondsSum += 1;
                    // 同步更新响应式数据
                    secondsSum.value = this.secondsSum;
                    console.log('this.secondsSum', this.secondsSum);
                    if (this.secondsSum >= 30) {
                        this.clearCountTimer();
                    // todo 上报
                    // this.secondsSum = 0;
                    }
                }
            }, 1000);
        }

        clearCountTimer() {
            clearInterval(this.countTimer);
        }

        // 暂停音频
        pause() {
            this.audio.pause();
            this.clearCountTimer();
        }

        stop() {
            this.clearCountTimer();
            this.audio.pause();
            this.audio.currentTime = 0;
        }

        replay() {
            this.audio.currentTime = 0;
            this.play();
        }

        clear() {
            this.stop();
            this.secondsSum = 0;
            secondsSum.value = 0;
        }

        // 播放音频
        play() {
            if (!this.audio.src)
                return;

            const playPromise = this.audio.play();
            if (playPromise !== undefined) {
                playPromise.then(() => {
                    console.log('Audio started playing successfully');
                    this.countSeconds();
                }).catch((error) => {
                    console.error('Audio play failed:', error);
                });
            }
            else {
                this.countSeconds();
            }
        }

        // 获取音频播放进度
        getProgress() {
            if (!this.audio.src || !this.audio.duration) {
                return 0;
            }
            return (this.audio.currentTime / this.audio.duration) * 100;
        }
    }

    const player = ref(new AudioPlayer());
    const ended = ref(false);
    const pause = ref(false);
    const playingAudioId = ref('');

    const playing = computed(() => playingAudioId.value && !ended.value && !pause.value);

    player.value.audio.addEventListener('ended', () => {
        ended.value = true;
        pause.value = false;
        playingAudioId.value = '';
    });

    player.value.audio.addEventListener('pause', () => {
        pause.value = true;
    });

    player.value.audio.addEventListener('play', () => {
        ended.value = false;
        pause.value = false;
    });

    player.value.audio.addEventListener('loadeddata', () => {
        duration.value = player.value.audio.duration;
    });

    // player.value.audio.addEventListener('timeupdate', () => {
    //     if (player.value.audio.currentTime <= 0.1) {
    //         ended.value = false;
    //     }
    //     pause.value = false;
    // });

    const state = reactive({
        user: {},
        value: 0,
        anchorWork: {},
        score: 0,
    });
    // 请求新对象
    const getNewWorker = async () => {
        try {
            const [res] = await getHeartbeatChallengeData();
            if (res.code !== 0) {
                return;
            }
            clearAudio();
            Object.assign(state, res.data);
            return res.code;
        }
        catch (error) {
            console.error('获取音乐任务失败', error.message);
        }
    };

    // 打分
    const rate = async (score) => {
        try {
            const [res] = await setScore({
                workerUid: state.anchorWork.workerUid,
                score,
            });
            if (res.code !== 0) {
                state.value = state.value + 1;
                state.score = score;
                return;
            }
            state.value = state.value + 1;
            state.score = score;
            return res.data;
        }
        catch (error) {
            console.error('上报音乐任务失败', error.message);
        }
    };

    const notLike = computed(() => state.score <= 3);

    function pauseAudio() {
        player.value.pause();
        pause.value = true;
    }

    function stopAudio() {
        // playingAudioId.value = '';
        console.log('暂停', pause.value);
        player.value.stop();
    }

    function clearAudio() {
        player.value.clear();
        ended.value = false;
        pause.value = false;
        playingAudioId.value = '';
    }

    function continueAudio() {
        player.value.play();
        pause.value = false;
        ended.value = false;
    }

    function playAudio(url) {
        // track({
        //     pageId: 'activity_page',
        //     event_id: 'play_click',
        //     general_param_1: JSON.stringify({
        //         tab: RANK_AREA_TEXT[baseStore.currentAreaId]?.replace('赛区', '') || '',
        //         schedule: RANK_STAGE_TEXT[baseStore.currentAreaSelectStageId] || '',
        //     }),
        // });

        console.log('playAudio called with url:', url);
        notPlay.value = true;
        if (url) {
            playingAudioId.value = url;
            player.value.setSrcAndPlay(url);
            console.log('Audio playing state:', !player.value.audio.paused);
        }
    }

    function handleVisibilityChange() {
        if (document.visibilityState === 'hidden') {
            stopAudio();
        }
    }

    function destroy() {
        if (player.value) {
            player.value.stop();
            player.value = null;
        }
    }

    onMounted(() => {
        window.addEventListener('visibilitychange', handleVisibilityChange);
    });

    onBeforeUnmount(() => {
        window.removeEventListener('visibilitychange', handleVisibilityChange);
    });

    window.beforeDestroyPage = () => {
        destroy();
    };

    return {
        ...toRefs(state),
        getNewWorker,
        playingAudioId,
        ended,
        pause,
        playing,
        notLike,
        playAudio,
        continueAudio,
        pauseAudio,
        stopAudio,
        rate,
        secondsSum, // 暴露 secondsSum
        duration,
        notPlay,
    };
});

export default useAudioPlayer;
