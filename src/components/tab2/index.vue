<template>
    <!-- 内容区域 -->
    <div class="relative z-1 flex flex-col items-center">
        <!-- 标题区域 -->
        <div
            class="bg-default relative h-[49px] w-[350px] flex items-center pl-62"
            :style="{ backgroundImage: `url(${requireImg('tab2/<EMAIL>')})` }"
        >
            <div class="text-[12px] text-[#FFEBC7] leading-[12px]">
                <div class="mb-6 text-13">8月18-25日每晚20点 <span class="text-[#F09E56]">开启闪耀官频巡演</span></div>
                <div v-if="currentScheduleStatus === 0">距下一场还剩<span class="text-[#F09E56]">{{ countdownDisplay[1] }}</span>时<span class="text-[#F09E56]">{{ countdownDisplay[2] }}</span>分<span class="text-[#F09E56]">{{ countdownDisplay[3] }}</span>秒 </div>
                <div
                    v-else
                    class="text-[#F09E56]">
                    官频巡演正在进行中!
                </div>
                <img
                    class="absolute right-0 top-0 h-[20px] w-[47px]"
                    src="@/assets/img/tab2/<EMAIL>"
                    @click="tourStore.handleReserve">
            </div>
        </div>

        <!-- 主播卡片区域 -->
        <div
            class="bg-default relative mb-10 mt-12 h-[325px] w-[375px] flex flex-col items-center"
            :style="{ backgroundImage: `url(${requireImg('tab2/<EMAIL>')})` }"
        >
            <div class="mt-[45px] text-[12px] text-[#FFFFFF]">星光日榜TOP3队伍的学员可登上次日官频巡演</div>
            <!-- 有主播报名的情况 -->
            <div
                v-if="hasAnchors"
                class="relative w-100%">
                <van-swipe
                    ref="swipeRef"
                    :autoplay="3000"
                    :show-indicators="false"
                    :loop="true"
                    @change="onSwipeChange">
                    <van-swipe-item
                        v-for="(anchor, index) in currentSchedule.anchors"
                        :key="anchor.anchorId"
                        class="w-100% flex justify-center"
                    >
                        <div
                            class="bg-default h-[260.5px] w-[236px] flex flex-col items-center px-5 py-15 text-center"
                            :style="{ backgroundImage: `url(${requireImg('tab2/<EMAIL>')})` }"
                            @click="onCardClick(anchor)">
                            <!-- 时间段 -->
                            <div class="mb-5 text-4 text-[#ffd700] font-bold">{{ anchor.timeSlot }}</div>

                            <!-- 主播信息 -->
                            <div class="mb-5">
                                <div class="relative mb-4 inline-block">
                                    <img
                                        class="h-20 w-20 border-3 border-white rounded-full object-cover"
                                        :src="anchor.avatar"
                                        :alt="anchor.nickname" />
                                    <div
                                        class="absolute bottom-0 right-0 rounded-2.5 px-2 py-0.5 text-2.5 font-bold"
                                        :class="anchor.isOnline ? 'bg-[#4caf50] text-white' : 'bg-[#757575] text-white'">
                                        {{ anchor.isOnline ? '在线' : '离线' }}
                                    </div>
                                </div>
                                <div class="break-all text-4 text-white font-bold leading-1.4">{{ anchor.nickname }}</div>
                            </div>

                            <!-- 关注按钮 (未开启状态) -->
                            <div v-if="currentScheduleStatus === 0">
                                <button
                                    class="cursor-pointer rounded-5 border-none px-6 py-2 text-3.5 font-bold transition-all duration-300"
                                    :class="anchor.isFollowed
                                        ? 'bg-white/20 text-[#b8b8b8] cursor-default'
                                        : 'bg-gradient-to-br from-[#667eea] to-[#764ba2] text-white hover:scale-105'"
                                    @click.stop="onFollowClick(anchor)">
                                    {{ anchor.isFollowed ? '已关注' : '关注Ta' }}
                                </button>
                            </div>
                        </div>
                    </van-swipe-item>
                </van-swipe>

                <!-- 手动切换按钮 -->
                <div class="pointer-events-none absolute left-0 right-0 top-1/2 z-10 flex justify-between -translate-y-1/2">
                    <button
                        class="pointer-events-auto h-10 w-10 flex cursor-pointer items-center justify-center rounded-full border-none bg-white/20 backdrop-blur-2.5 transition-all duration-300 -ml-5 hover:(scale-110 bg-white/30)"
                        @click="swipePrev">
                        <img
                            :src="requireImg('tab2/<EMAIL>')"
                            alt="上一个"
                            class="h-5 w-5 brightness-0 invert" />
                    </button>
                    <button
                        class="pointer-events-auto h-10 w-10 flex cursor-pointer items-center justify-center rounded-full border-none bg-white/20 backdrop-blur-2.5 transition-all duration-300 -mr-5 hover:(scale-110 bg-white/30)"
                        @click="swipeNext">
                        <img
                            :src="requireImg('tab2/<EMAIL>')"
                            alt="下一个"
                            class="h-5 w-5 brightness-0 invert" />
                    </button>
                </div>
            </div>

            <!-- 无主播报名的缺省状态 -->
            <div
                v-else
                class="bg-default h-[260.5px] w-[236px] flex flex-col items-center px-5 py-15 text-center"
                :style="{ backgroundImage: `url(${requireImg('tab2/<EMAIL>')})` }"
            >
                <div class="mt-5">
                    <img
                        :src="requireImg('tab2/<EMAIL>')"
                        alt="暂无主播"
                        class="h-[63px] w-[63px]" />
                </div>
                <div class="mt-53">
                    <p class="my-2 text-14 text-[#DFDFDF] leading-1.5">参演嘉宾正在赶来路上~</p>
                    <p class="my-2 text-14 text-[#DFDFDF] leading-1.5">敬请期待</p>
                </div>
            </div>
        </div>
        <div
            class="bg-default relative mx-16 mt-12 h-[66px] w-[350px] items-center justify-center"
            :style="{ backgroundImage: `url(${requireImg('tab2/<EMAIL>')})` }"
        >
            <img
                class="h-[42px] w-[42px]"
                src="@/assets/img/tab2/<EMAIL>"
                alt="">
            <img
                class="absolute left-11 top-[-5.5px] h-[18.5px] w-[55px]"
                src="@/assets/img/tab2/<EMAIL>"
                alt="">
        </div>
    </div>
</template>

<script setup name="Tab2">
// src/components/tab2/index.vue
import useTourStore from '@/stores/modules/use-tour-store';
import { countdown } from '@/utils';

const tourStore = useTourStore();
const {
    currentSchedule,
    currentScheduleStatus,
    currentAnchorId,
    hasAnchors,
    countdownTime,
} = storeToRefs(tourStore);

// 轮播组件引用
const swipeRef = ref(null);

// 倒计时显示
const countdownDisplay = computed(() => {
    return countdown(countdownTime.value);
});

// 当前轮播索引
const currentSwipeIndex = ref(0);

/**
 * 初始化数据
 */
async function initTourData() {
    try {
        await tourStore.fetchTourList();

        // 如果是进行中状态，定位到当前表演的主播
        if (currentScheduleStatus.value === 1 && currentAnchorId.value) {
            await nextTick();
            locateCurrentAnchor();
        }
    }
    catch (error) {
        console.error('初始化巡演数据失败:', error);
    }
}

/**
 * 定位到当前表演的主播
 */
function locateCurrentAnchor() {
    if (!currentSchedule.value || !currentAnchorId.value || !swipeRef.value)
        return;

    const anchorIndex = currentSchedule.value.anchors.findIndex(
        anchor => anchor.anchorId === currentAnchorId.value,
    );

    if (anchorIndex !== -1) {
        swipeRef.value.swipeTo(anchorIndex);
        currentSwipeIndex.value = anchorIndex;
    }
}

/**
 * 轮播切换事件
 */
function onSwipeChange(index) {
    currentSwipeIndex.value = index;
}

/**
 * 手动切换到上一张
 */
function swipePrev() {
    if (swipeRef.value) {
        swipeRef.value.prev();
    }
}

/**
 * 手动切换到下一张
 */
function swipeNext() {
    if (swipeRef.value) {
        swipeRef.value.next();
    }
}

/**
 * 卡片点击事件
 */
function onCardClick(anchor) {
    // 只有进行中状态且是当前表演主播才可点击
    if (currentScheduleStatus.value === 1 && anchor.anchorId === currentAnchorId.value) {
        onListenClick(anchor);
    }
}

/**
 * 关注按钮点击
 */
async function onFollowClick(anchor) {
    if (anchor.isFollowed) {
        await tourStore.handleUnfollowAnchor(anchor.anchorId);
    }
    else {
        await tourStore.handleFollowAnchor(anchor.anchorId);
    }
}

/**
 * 去收听按钮点击
 */
function onListenClick(anchor) {
    tourStore.goToAnchorRoom(anchor.roomId);
}

// 组件挂载时初始化数据
onMounted(() => {
    initTourData();
});

// 组件激活时刷新数据
onActivated(() => {
    initTourData();
});

// 监听当前主播变化，自动定位
watch(currentAnchorId, () => {
    if (currentScheduleStatus.value === 1) {
        nextTick(() => {
            locateCurrentAnchor();
        });
    }
});
</script>
