<template>
    <van-swipe
        ref="swipeRef"
        class="swipe"
        autoplay="3000"
        duration="1000"
        :show-indicators="false">
        <van-swipe-item class="swipe-item hour">
            <div class="title"></div>
            <div class="text">
                19-23点，单小时收礼>10w豆可累计5%加成比例，23-24点开始享受加成!
            </div>
            <div class="content">
                <div class="content-left">
                    <div class="content-left__title">
                        {{ isHourBuffStart ? '' : '预计' }}加成比例：
                        <span>{{ finishHourList?.length * challengeHourBuffPercent }}%</span>
                    </div>
                    <div class="content-left__value">
                        <div
                            v-if="!isHourBuffStart"
                            class="grey">
                            --待23点开启--
                        </div>
                        <template v-else>
                            小时加成值：
                            <span>{{ omitValue(buffValueData?.hour) }}</span>
                        </template>
                    </div>
                </div>
                <div class="content-hour">
                    <div class="content-hour-line"></div>
                    <div
                        v-for="hourItem in challengeHourBuffList"
                        :key="hourItem.text"
                        class="content-hour-item">
                        {{ hourItem.text }}

                        <img
                            v-if="
                                !finishHourList.includes(hourItem.matchValue)
                                    && compareHourBuff(hourItem.matchValue) < 0
                            "
                            :src="requireAssets('img/<EMAIL>')"
                            class="icon" />
                        <div
                            v-if="
                                !finishHourList.includes(hourItem.matchValue)
                                    && compareHourBuff(hourItem.matchValue) === 0
                            "
                            class="gap">
                            差{{ omitValue(100000 - noBuffHourValue) }}豆
                        </div>
                        <template v-if="finishHourList.includes(hourItem.matchValue)">
                            <img
                                :src="requireAssets('img/<EMAIL>')"
                                class="icon" />
                            <span>+{{ challengeHourBuffPercent }}%</span>
                        </template>

                        <div
                            v-if="compareHourBuff(hourItem.matchValue) > 0"
                            class="notyet">
                            暂未开启
                        </div>
                    </div>
                </div>
            </div>
        </van-swipe-item>
        <van-swipe-item class="swipe-item knight">
            <div class="title"></div>
            <div class="text">
                比赛期间,开启听听收到骑士团用户的礼物,可获得10%的加成
            </div>
            <div class="content">
                <div class="content-left">
                    <div class="content-left__title">
                        骑士加成值：
                    </div>
                    <div class="content-left__value">
                        <span>{{ omitValue(buffValueData?.knight) }}</span>
                    </div>
                </div>
                <div
                    class="content-knight"
                    :class="{
                        empty: !knightList?.length,
                    }">
                    <div
                        v-for="knight in knightList"
                        :key="knight?.username"
                        class="knight-item">
                        <img :src="getAvatar(knight?.username)">
                    </div>
                </div>
            </div>
        </van-swipe-item>
    </van-swipe>
    <div
        class="next"
        @click="swipeRef?.next"></div>
    <div
        class="prev"
        @click="swipeRef?.prev"></div>
</template>

<script setup>
import { reactive, ref } from 'vue';

const swipeRef = ref(null);
</script>

<style lang="less" scoped>

</style>
