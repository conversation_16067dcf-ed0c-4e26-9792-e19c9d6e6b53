<template>
    <div
        class="rank-item"
        :class="{ nomal_bg: !isMe }">
        <div class="left">
            <div class="sort">{{ item.rank }}</div>
            <div class="user">
                <div class="user-item">
                    <div class="pic">
                        <img
                            class="avatar"
                            :src="getAvatar(item.userInfo.username)"
                            @click="toPerson(item?.userInfo?.username)"
                        />
                        <img
                            class="label"
                            :src="requireImg('tab1/<EMAIL>')" />
                    </div>
                    <div class="nickname">{{ item.userInfo.nickname }}</div>
                    <room-status
                        class="z050"
                        :item="item" />
                </div>
                <img
                    class="icon"
                    :src="requireImg('tab1/<EMAIL>')" />
                <div class="user-item">
                    <div class="pic">
                        <img
                            class="avatar"
                            :src="getAvatar(item.discipleInfo.userInfo.username)"
                            @click="toPerson(item?.discipleInfo?.userInfo?.username)"
                        />
                        <img
                            class="label"
                            :src="requireImg('tab1/<EMAIL>')" />
                        <room-status
                            class="z050"
                            :item="item" />
                    </div>
                    <div class="nickname">{{ item.discipleInfo.userInfo.nickname }}</div>
                </div>
            </div>
        </div>
        <div class="right">
            <div class="value">
                <img
                    class="icon"
                    :src="requireImg('tab1/<EMAIL>')" />+{{ omitValue(item.value) }}
            </div>
            <div
                v-if="item.rank <= 3"
                class="money">
                导师预计得1400元
            </div>
        </div>
    </div>
</template>

<script setup>
const props = defineProps({
    item: {
        type: Object,
        default: () => { },
    },
    // 底部浮层
    isMe: {
        type: Boolean,
        default: false,
    },
    // top3竖排样式
    columnTop3Style: {
        type: Boolean,
        default: false,
    },
});
</script>

<style lang="less" scoped>
.nomal_bg {
    .full-bg('@/assets/img/tab1/<EMAIL>');
}
.rank-item {
    width: 350px;
    height: 80px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 0 auto 10px;
    position: relative;
    padding-top: 5px;
    .left {
        display: flex;
        justify-content: flex-start;
        height: inherit;
        align-items: center;

        .sort {
            width: 40px;
            text-align: center;
            font-size: 13px;
            font-weight: 700;
            text-align: center;
            color: #e8dec2;
        }

        .user {
            display: flex;
            justify-content: flex-start;
            align-items: center;

            .icon {
                width: 27px;
                height: 27px;
            }

            &-item {
                width: 69px;

                .pic {
                    width: 46px;
                    height: 46px;
                    position: relative;
                    margin: 0 auto 4px;

                    .avatar {
                        width: 42px;
                        height: 42px;
                        border-radius: 50%;
                        border: 2px solid #d2d3d3;
                    }
                    .label {
                        position: absolute;
                        left: 50%;
                        transform: translateX(-50%);
                        top: -5px;
                        z-index: 2;
                        width: 25px;
                        height: 11px;
                    }
                }

                .nickname {
                    width: 100%;
                    .one-line();
                    font-size: 12px;
                    font-weight: 400;
                    text-align: left;
                    color: #e5e5e5;
                }
            }
        }
    }

    .right {
        .value {
            display: flex;
            justify-content: flex-end;
            align-items: center;
            height: inherit;
            margin-top: 8px;
            padding-right: 10px;

            .icon {
                width: 16px;
                height: 16px;
                margin-right: 2px;
            }

            font-size: 14px;
            font-weight: normal;
            text-align: left;
            color: #ffe29c;
        }
        .money {
            .pic-bg(url('@/assets/img/tab1/<EMAIL>'), 112px, 17px);
            position: absolute;
            right: 0;
            bottom: 0;
            z-index: 2;
            font-size: 11px;
            font-weight: normal;
            text-align: left;
            color: #b94e14;
            line-height: 17px;
            text-align: center;
            padding-left: 8px;
        }
    }
}
</style>
