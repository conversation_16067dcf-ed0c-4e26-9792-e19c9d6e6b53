<template>
    <popup-container
        v-model:show="isShow"
        :close-on-click-overlay="true"
    >
        <div class="daily-challenge-popup">
            <div class="top">
                <div class="top-left">
                    <img
                        class="avatar"
                        :src="getAvatar(initData?.userInfo?.username)" />我的身份：{{ initData?.identity === 1 ? '导师' : '学员' }}
                </div>
                <div class="top-right">
                    今日星光值：{{ omitValue(myStarlightInfo?.myStarlight) }}<img
                        class="tips-icon2"
                        :src="requireImg('tab1/<EMAIL>')"
                        @click="showTipsContent = !showTipsContent" />
                    <div
                        v-show="showTipsContent"
                        ref="tipsContent2"
                        class="tipsContent2">
                        *闪耀官频巡演的星光值不计入噢
                    </div>
                </div>
            </div>
            <div class="body">
                <p class="tips">每日自身星光值达到2000即可根据身份获得对应奖励</p>
                <div class="award">
                    <div
                        class="award-left">
                        <Tab1-reward-item :item="rewardItem" />
                        <template v-if="initData?.identity === 1">
                            <img
                                v-if="myStarlightInfo?.rewardStatus === 2"
                                class="btn"
                                :src="requireImg('tab1/<EMAIL>')" />
                            <!-- 未获得 -->
                            <img
                                v-else-if="myStarlightInfo?.rewardStatus === 0"
                                class="btn"
                                :src="requireImg('tab1/<EMAIL>')" />
                            <img
                                v-else-if="myStarlightInfo?.rewardStatus === 1"
                                class="btn"
                                :src="requireImg('tab1/<EMAIL>')"
                                @click="toReceive" />
                        </template>
                        <!-- 已领取 -->
                    </div>
                    <div
                        class="award-right">
                        <Tab1-reward-item :item="rewardItem" />
                        <Tab1-reward-item :item="rewardItem" />
                        <div class="today-left">今日剩余{{ myStarlightInfo?.rewardNum }}</div>
                        <template v-if="initData?.identity === 2">
                            <!-- 已领取 -->
                            <img
                                v-if="myStarlightInfo?.rewardStatus === 2"
                                class="btn"
                                :src="requireImg('tab1/<EMAIL>')" />
                            <!-- 未获得 -->
                            <img
                                v-else-if="myStarlightInfo?.rewardStatus === 0"
                                class="btn"
                                :src="requireImg('tab1/<EMAIL>')" />
                            <img
                                v-else-if="myStarlightInfo?.rewardStatus === 1"
                                class="btn"
                                :src="requireImg('tab1/<EMAIL>')"
                                @click="toReceive" />
                        </template>
                    </div>
                </div>
                <p class="tips">每天24点,在完成每日挑战的达人中随机抽取20人额外发放星光幸运儿大v认证奖励!</p>
                <div class="lucky-ones">
                    <div class="user">
                        <div
                            v-for="item in dailyLuckyList"
                            class="user-item">
                            <div class="pic">
                                <img
                                    class="avatar"
                                    :src="getAvatar(item?.username)" />
                            </div>
                            <div class="nickname">{{ item?.nickname }}</div>
                        </div>
                        <div
                            v-if="!dailyLuckyList.length"
                            class="no-data">
                            暂无数据
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </popup-container>
</template>

<script setup>
import useTab1Store from './hooks/use-tab1-store';
import { recordArr } from './const';
import useInitStore from '@/stores/modules/use-init-store';
import { createExternalClickHandler } from '@/utils/index';

const { serverTime, initData } = storeToRefs(useInitStore());
const { getMyStarlightApi, receiveDailyRewardApi, getDailyLuckyListApi } = useTab1Store();
const { myStarlightInfo, dailyLuckyList } = storeToRefs(useTab1Store());
const isShow = ref(false);
const showTipsContent = ref(false);
const tipsContent2 = ref();
const toReceive = async () => {
    const { code } = await receiveDailyRewardApi();
    console.log('code', code);
    if (code === 0) {
        showToast('领取成功');
        getMyStarlightApi();
    }
};
let handler = null;
const rewardItem = { pic: 'default_avatar_no_compress.png', name: '奖励xxxxxx', desc: '奖励描述xxxxx' };
useEventBus('daily-challenge-popup').on(async ({ show = true }) => {
    isShow.value = show;
    getMyStarlightApi();
    getDailyLuckyListApi();
    await nextTick();
    // 点击元素以外区域触发
    handler = createExternalClickHandler({
        target: tipsContent2.value,
        excludeSelectors: ['.tipsContent2', '.tips-icon2'], // 支持多个排除元素
        callback: () => {
            // console.log('触发外部点击');
            if (showTipsContent.value) {
                showTipsContent.value = false;
            }
        // handler.stop(); // 可选：关闭后停止监听
        },
    });
    // 初始化显示并启动监听
    handler.start();
});
</script>

<style lang="less" scoped>
.daily-challenge-popup {
    .pic-bg(url('@/assets/img/tab1/<EMAIL>'), 375px, 456px);
    padding-top: 52px;
    .top {
        width: 100%;
        display: flex;
        justify-content: space-between;
        padding-left: 25px;
        padding-right: 14px;
        &-left {
            display: flex;
            justify-content: flex-start;
            font-size: 16px;
            font-weight: normal;
            text-align: left;
            color: #ffe29c;
            height: 28px;
            align-items: center;
            .avatar {
                width: 28px;
                height: 28px;
                border-radius: 50%;
                margin-right: 6px;
            }
        }
        &-right {
            font-size: 14px;
            font-weight: normal;
            text-align: left;
            color: #ffe29c;
            display: flex;
            justify-content: flex-end;
            align-items: center;
            height: 28px;
            position: relative;
            .tips-icon2 {
                width: 13px;
                height: 13px;
                margin-left: 4px;
                margin-top: -10px;
            }
            .tipsContent2 {
                .pic-bg(url('@/assets/img/tab1/<EMAIL>'), 206px, 45px);
                font-size: 12px;
                font-weight: normal;
                text-align: center;
                color: #df8c2a;
                position: absolute;
                line-height: 45px;
                top: 16px;
                right: -13px;
                z-index: 3;
            }
        }
    }
    .body {
        width: 100%;
        padding: 20px 30px 0;
        .tips {
            font-size: 13px;
            font-weight: normal;
            text-align: center;
            color: #d2cee5;
            margin-bottom: 14px;
        }
        .award {
            display: flex;
            justify-content: center;
            margin-bottom: 10px;
            &-left {
                .pic-bg(url('@/assets/img/tab1/<EMAIL>'), 104px, 154px);
                padding-top: 30px;
                position: relative;
                display: flex;
                justify-content: center;
                margin: 0 9px;
            }
            &-right {
                .pic-bg(url('@/assets/img/tab1/<EMAIL>'), 195px, 154px);
                display: flex;
                justify-content: center;
                padding-top: 30px;
                position: relative;
                margin: 0 9px;
                .today-left {
                    width: 60px;
                    height: 16px;
                    line-height: 16px;
                    background: rgba(0, 0, 0, 0.6);
                    font-size: 9px;
                    position: absolute;
                    top: 28px;
                    left: 26px;
                    z-index: 2;
                    border-radius: 6px;
                    text-align: center;
                    white-space: nowrap;
                    color: #fff;
                }
            }
            .btn {
                position: absolute;
                bottom: 8px;
                left: 50%;
                width: 74px;
                height: 24px;
                transform: translateX(-50%);
            }
            .gray-btn {
                filter: grayscale(100%);
            }
        }
        .lucky-ones {
            padding-top: 33px;
            .pic-bg(url('@/assets/img/tab1/<EMAIL>'), 317px, 101px);
            .user {
                width: 100%;
                overflow-x: scroll;
                overflow-y: hidden;
                height: 60px;
                display: flex;
                justify-content: flex-start;
                &-item {
                    width: 50px;
                    margin-right: 8px;
                    .pic {
                        width: 42px;
                        height: 42px;
                        margin: 0 auto;
                        .avatar {
                            width: 38px;
                            height: 38px;
                            border-radius: 50%;
                            border: 2px solid #d2d3d3;
                        }
                    }
                    .nickname {
                        font-size: 10px;
                        font-weight: 400;
                        text-align: center;
                        .one-line();
                        color: #e5e5e5;
                        line-height: 18px;
                    }
                }
            }
        }
        .no-data {
            width: 100%;
            font-size: 12px;
            font-weight: normal;
            text-align: center;
            color: #d2cee5;
            line-height: 24px;
            text-align: center;
            margin-top: 20px;
        }
    }
}
</style>
