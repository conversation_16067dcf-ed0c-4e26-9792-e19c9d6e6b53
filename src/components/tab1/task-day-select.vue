<template>
    <div class="total-day-select">
        <!-- <div
            class="total-text"
            :class="[{ 'is-active': dateSelectStore.currentTab === dateSelectStore.totalKey }]"
            @click="changeTab(dateSelectStore.totalKey)">
            <div>总榜</div>
        </div> -->
        <div
            ref="wrapper"
            class="day-select">
            <div
                v-for="(item, index) in dateSelectStore.dates"
                ref="itemRefs"
                :key="`day-tab-${index}`"
                class="tab-item"
                :class="[{ 'is-active': dateSelectStore.currentTab === item.value }]"
                @click="changeTab(item.value)">
                {{ item.dateStr }}
                <!-- <div
                    v-if="dateSelectStore.currentTab === item.value"
                    class="active-line"></div> -->
            </div>
        </div>
    </div>
</template>

<script setup>
import useDateSelectStore from './hooks/use-record-date-select-store';
import { scrollIntoViewMiddle } from '@/utils';

const emit = defineEmits(['changeDate']);

const dateSelectStore = useDateSelectStore();

const itemRefs = ref([]);
const wrapper = ref();

const changeTab = (tab) => {
    if (dateSelectStore.currentTab === tab)
        return;
    dateSelectStore.setCurrentTab(tab);
};

const tofocus = () => {
    const selectedIndex = dateSelectStore.dates.findIndex(item => item.value === dateSelectStore.currentTab);
    if (selectedIndex <= 0) {
        // 默认滚动最左
        wrapper.value?.scrollTo({
            left: 0,
            behavior: 'smooth',
        });
        return;
    }
    const itemRef = itemRefs.value[selectedIndex];
    if (!itemRef) {
        // 默认滚动最左
        wrapper.value?.scrollTo({
            left: 0,
        });
    }
    scrollIntoViewMiddle(itemRef, wrapper.value);
};

watch(
    () => dateSelectStore.currentTab,
    (curDate) => {
        emit('changeDate', curDate);
        nextTick(() => {
            tofocus();
        });
    },
    {
        immediate: true,
    },
);
</script>

<style lang="less" scoped>
.total-day-select {
    display: flex;
    align-items: center;
    margin: 10px auto 0;
    width: 350px;
    .full-bg('@/assets/img/tab1/<EMAIL>');
    .total-text,
    .tab-item {
        flex-shrink: 0;
        width: 65px;
        font-size: 13px;
        text-align: center;
        color: #bba6a1;
        white-space: nowrap;
        height: 24px;
        line-height: 24px;
        // border-radius: 3.5px;
        position: relative;
        margin-top: 10px;
        &.is-active {
            // font-weight: bold;
            color: #fdff74;
            width: 76px;
            height: 29px;
            // .full-bg('@/assets/img/tab1/<EMAIL>');
        }
        .active-line {
            position: absolute;
            bottom: 2px;
            left: 50%;
            transform: translateX(-50%);
            width: 24px;
            height: 3px;
            background: #fe68a1;
            border-radius: 2px;
            z-index: 2;
        }
        // &.is-active::after {
        //     position: absolute;
        //     bottom: -1px;
        //     left: calc(50% - 7.5px);
        //     content: '';
        //     width: 15px;
        //     height: 4px;
        //     background: linear-gradient(90deg, #5062e8 94%, #24308b 100%);
        //     border-radius: 2px;
        // }
    }

    .day-select {
        position: relative;
        flex: 1;
        display: flex;
        overflow-x: auto;
    }

    .tab-item + .tab-item {
        margin-left: 11px;
    }
}
</style>
