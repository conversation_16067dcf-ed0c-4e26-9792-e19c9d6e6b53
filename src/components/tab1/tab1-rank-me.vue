<template>
    <div class="rank-me">
        <tab1-rank-item
            :item="self"
            :is-me="true" />
    </div>
</template>

<script setup>
import useRankStore from './hooks/use-rank-store';
import useInitStore from '@/stores/modules/use-init-store';

const { self } = storeToRefs(useRankStore());
</script>

<style lang="less" scoped>
.rank-me {
    .pic-bg(url('@/assets/img/tab1/<EMAIL>'), 375px, 82px);
    position: fixed;
    bottom: 79px;
    left: 0;
    z-index: 2;
}
</style>
