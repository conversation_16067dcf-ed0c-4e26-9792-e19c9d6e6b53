<template>
    <div class="reward">
        <div class="reward-pic">
            <img
                v-if="item?.pic"
                :src="requireImg(item?.pic)"
                alt="">
        </div>
        <div class="reward-name">{{ item?.name }}</div>
        <div class="reward-desc">{{ item?.desc }}</div>
    </div>
</template>

<script setup>
const props = defineProps({
    item: {
        type: Object,
        default() {
            return {};
        },
    },
});
</script>

<style lang="less" scoped>
.reward {
    width: 58px;
    margin: 0 6px;
    &-pic {
        width: 100%;
        height: 58px;
        .full-bg('@/assets/img/tab1/<EMAIL>');
        display: flex;
        justify-content: center;
        align-items: center;
        margin-bottom: 3px;
        img {
            max-width: 80%;
            max-height: 80%;
        }
    }
    &-name {
        font-size: 11px;
        font-weight: 400;
        text-align: center;
        color: #ffebc7;
        width: 100%;
        .one-line();
        margin-bottom: 2px;
    }
    &-desc {
        font-size: 11px;
        font-weight: 400;
        text-align: center;
        color: #ffebc7;
        .one-line();
        margin-bottom: 3px;
    }
}
</style>
