import { defineStore } from 'pinia';
import dayjs from 'dayjs';
import isSameOrAfter from 'dayjs/esm/plugin/isSameOrAfter';
import isSameOrBefore from 'dayjs/esm/plugin/isSameOrBefore';
import curstomParseFormat from 'dayjs/plugin/customParseFormat';
import useInitStore from '@/stores/modules/use-init-store';
import { dayjsToDayBegin, dayjsToDayEnd, getIncludesDays } from '@/utils';

dayjs.extend(isSameOrAfter);
dayjs.extend(isSameOrBefore);
dayjs.extend(curstomParseFormat);

const useDateSelectStore = defineStore('dateSelect', () => {
    const totalKey = 'total';

    const state = reactive({
        currentTab: totalKey,
    });

    const { serverTime, initData } = storeToRefs(useInitStore());
    const setCurrentTab = (key) => {
        // console.log('key', key);
        // const selectDate = dayjs(key, 'YYYYMMDD').tz();
        // console.log('selectDate', selectDate);
        const curT = dayjs.unix(serverTime.value).tz().format('YYYYMMDD');
        if (key !== 'total' && curT < key) {
            showToast('暂未开启');
            return;
        }
        state.currentTab = key;
    };

    const isTotal = computed(() => state.currentTab === totalKey);

    // 格式化的日期数组
    const dates = computed(() => {
        const startTime = initData.value.startTime;
        const endTime = initData.value.endTime;

        const diffDaysNum = getIncludesDays(startTime, endTime);
        let begin = dayjsToDayBegin(dayjs.unix(startTime).tz());
        const dateArr = [];
        for (let i = 0; i < diffDaysNum; i++) {
            const cur = begin.clone();
            dateArr.push(cur);
            begin = begin.add(1, 'day');
        }
        return dateArr.map((date, index) => ({
            date,
            value: date.format('YYYYMMDD'),
            dateStr: date.format('M月D日'),
        }));
    });

    watch(dates, (newDates) => {
        if (newDates.length) {
            const curT = dayjs.unix(serverTime.value).tz();
            const beginT = dayjsToDayBegin(dayjs.unix(initData.value.startTime).tz());
            const endT = dayjsToDayEnd(dayjs.unix(initData.value.endTime).tz());
            const isInActivityDate = curT.isSameOrAfter(beginT) && curT.isSameOrBefore(endT);

            const { total } = parseUrlQuery();
            if (isInActivityDate && !total) { // 在活动期间且没有total参数
                setCurrentTab(curT.format('YYYYMMDD'));
            }
        }
    }, { immediate: true });

    return {
        ...toRefs(state),
        dates,
        isTotal,
        totalKey,
        setCurrentTab,
    };
});

export default useDateSelectStore;
