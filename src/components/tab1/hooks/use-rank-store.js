import { defineStore } from 'pinia';
import useDateSelectStore from './use-date-select-store';
import { getStarlightRank } from '@/api';

const useRankStore = defineStore('rank', () => {
    const state = reactive({
        rankList: [],
        self: {},
    });

    const dateSelectStore = useDateSelectStore();

    const pageConfig = {
        page: 1,
        size: 10,
        totalPage: 1,
    };

    const initPageConfig = () => {
        pageConfig.page = 1;
        pageConfig.totalPage = 1;
    };

    const getRankList = async (payload) => {
        const loading = showLoading();
        const [{ code, data }] = await getStarlightRank(payload);
        loading.close();
        console.log('getRankList', data);
        if (code === 0) {
            return data;
        }
        return {};
    };

    let isIniting = false;

    const setTab = (key) => {
        if (isIniting)
            return;
        state.tabKey = key;
    };

    const initRankList = async (payload) => {
        if (isIniting)
            return;
        isIniting = true;
        initPageConfig();
        const { page, size } = pageConfig;
        // console.log('payload', payload);
        const { total, list, self } = await getRankList({ page, size, ...payload, ...(dateSelectStore.currentTab === 'total' ? {} : { date: dateSelectStore.currentTab }) });
        pageConfig.totalPage = Math.ceil(total / size) || 1;
        state.rankList = list || [];
        state.self = self || {};
        isIniting = false;
    };

    let isLoadiing = false;
    const loadMore = async (payload) => {
        if (isLoadiing || pageConfig.page >= pageConfig.totalPage)
            return;
        isLoadiing = true;
        pageConfig.page += 1;
        const { page, size } = pageConfig;
        const { list } = await getRankList({ page, size, ...payload, ...(dateSelectStore.currentTab === 'total' ? {} : { date: dateSelectStore.currentTab }) });
        state.rankList = state.rankList.concat(list || []);
        isLoadiing = false;
    };

    return {
        ...toRefs(state),
        setTab,
        getRankList,
        initRankList,
        loadMore,
    };
});

export default useRankStore;
