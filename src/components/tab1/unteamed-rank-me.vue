<template>
    <div class="rank-item">
        <div class="left">
            <div class="sort">未组队</div>
            <div class="user">
                <div
                    v-if="initData?.identity === 1"
                    class="user-item">
                    <div class="pic">
                        <img
                            class="label"
                            :src="requireImg('tab1/<EMAIL>')" />
                        <img
                            class="avatar"
                            :src="getAvatar(initData?.userInfo?.username)" />
                    </div>
                    <div class="nickname">{{ initData?.userInfo?.nickname }}</div>
                </div>
                <div
                    v-else
                    class="user-item">
                    <div class="pic">
                        <img
                            class="label"
                            :src="requireImg('tab1/<EMAIL>')" />
                        <img
                            class="add"
                            :src="requireImg('tab1/<EMAIL>')" />
                    </div>
                    <div class="nickname"></div>
                </div>
                <img
                    class="icon"
                    :src="requireImg('tab1/<EMAIL>')" />
                <div
                    v-if="initData?.identity === 2"
                    class="user-item">
                    <div class="pic">
                        <img
                            class="label"
                            :src="requireImg('tab1/<EMAIL>')" />
                        <img
                            class="avatar"
                            :src="getAvatar(initData?.userInfo?.username)" />
                    </div>
                    <div class="nickname">{{ initData?.userInfo?.nickname }}</div>
                </div>
                <div
                    v-else
                    class="user-item">
                    <div class="pic">
                        <img
                            class="label"
                            :src="requireImg('tab1/<EMAIL>')" />
                        <img
                            class="add"
                            :src="requireImg('tab1/<EMAIL>')" />
                    </div>
                    <div class="nickname"></div>
                </div>
            </div>
        </div>
        <div class="right">
            <!-- <div class="value">
                <img
                    class="icon"
                    :src="requireImg('tab1/<EMAIL>')" />+9.99W
            </div> -->
            <img
                class="team-up"
                :src="requireImg('tab1/<EMAIL>')"
                @click="useEventBus('team-up-popup').emit({ show: true })" />
        </div>
    </div>
</template>

<script setup>
import useInitStore from '@/stores/modules/use-init-store';

const { initData } = storeToRefs(useInitStore());
</script>

<style lang="less" scoped>
.rank-item {
    .pic-bg(url('@/assets/img/tab1/<EMAIL>'), 375px, 82px);
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: fixed;
    bottom: 79px;
    left: 0;
    z-index: 2;
    padding-left: 10px;
    .left {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        .sort {
            width: 40px;
            text-align: center;
            font-size: 13px;
            font-weight: 700;
            text-align: center;
            color: #e8dec2;
        }

        .user {
            display: flex;
            justify-content: flex-start;
            align-items: center;

            .icon {
                width: 27px;
                height: 27px;
            }

            &-item {
                width: 69px;

                .pic {
                    width: 46px;
                    height: 46px;
                    position: relative;
                    margin: 0 auto 4px;

                    .avatar {
                        width: 42px;
                        height: 42px;
                        border-radius: 50%;
                        border: 2px solid #d2d3d3;
                    }
                    .add {
                        width: 46px;
                        height: 46px;
                    }
                    .label {
                        position: absolute;
                        left: 50%;
                        transform: translateX(-50%);
                        top: -5px;
                        z-index: 2;
                        width: 25px;
                        height: 11px;
                    }
                }

                .nickname {
                    width: 100%;
                    .one-line();
                    font-size: 12px;
                    font-weight: 400;
                    text-align: left;
                    color: #e5e5e5;
                }
            }
        }
    }

    .right {
        padding-right: 22px;
        .value {
            display: flex;
            justify-content: flex-end;
            align-items: center;
            height: inherit;
            margin-top: 8px;
            padding-right: 10px;

            .icon {
                width: 16px;
                height: 16px;
                margin-right: 2px;
            }

            font-size: 14px;
            font-weight: normal;
            text-align: left;
            color: #ffe29c;
        }
        .team-up {
            width: 93px;
            height: 32px;
        }
    }
}
</style>
