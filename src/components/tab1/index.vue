<template>
    <div>
        <div class="record-box">
            <p>·达人每日完成活动任务可获得星光值</p>
            <p>·学员考核S/A级的队伍可得20%/10%加成</p>
            <img
                class="btn"
                :src="requireImg('tab1/<EMAIL>')"
                alt=""
                @click="useEventBus('my-record-popup').emit({ show: true })">
        </div>
        <Total-day-select
            class="total-day-select-box"
            @change-date="changeDate" />
        <div
            v-if="currentTab !== 'total'"
            class="area-box">
            <div
                v-for="(item, index) in areaArr"
                :class="{ active: areaIndex === index }"
                @click="changeArea(index)">
                {{ item }}区
            </div>
        </div>
        <div class="reward-box total_bg">
            <p>需成功组队后才可上榜,接活动期间队伍累计星光值排名</p>
            <div class="rewards">
                <Tab1-reward-item
                    v-for="(item, index) in rewardsArr"
                    :item="item" />
            </div>
        </div>
        <div
            class="rank-box"
            :class="{ 'pb-[160px]': initData?.identity }">
            <tab1-rank-item
                v-for="(item, index) in rankList"
                :item="item" />
        </div>
        <unteamed-rank-me v-if="initData?.identity && !Object.keys(self).length" />
        <tab1-rank-me v-else-if="initData?.identity" />
        <my-record-popup />
        <team-up-popup />
        <confirm-team-dialog />
    </div>
</template>

<script setup>
import useRankStore from './hooks/use-rank-store';
import useDateSelectStore from './hooks/use-date-select-store';
import useInitStore from '@/stores/modules/use-init-store';

const rankStore = useRankStore();
const dateSelectStore = useDateSelectStore();
const { currentTab } = storeToRefs(useDateSelectStore());
const { serverTime, initData } = storeToRefs(useInitStore());
const { rankList, self } = storeToRefs(useRankStore());
const areaArr = ['A', 'B', 'C', 'D', 'E'];
const areaIndex = ref(-1);
const changeDate = () => {
    console.log('changeDate:', currentTab.value);
    setTimeout(() => {
        rankStore.initRankList({ ...(currentTab.value === 'total' ? {} : { dailyTeamRaceId: areaArr[areaIndex.value] }) });
    }, 100);
};
const rewardsArr = [
    { pic: 'default_avatar_no_compress.png', name: '奖励xxxxxx', desc: '奖励描述xxxxx' },
    { pic: 'default_avatar_no_compress.png', name: '奖励xxxxxx', desc: '奖励描述xxxxx' },
    { pic: 'default_avatar_no_compress.png', name: '奖励xxxxxx', desc: '奖励描述xxxxx' },
    { pic: 'default_avatar_no_compress.png', name: '奖励xxxxxx', desc: '奖励描述xxxxx' },
    { pic: 'default_avatar_no_compress.png', name: '奖励xxxxxx', desc: '奖励描述xxxxx' },
];

const changeArea = (index) => {
    if (areaIndex.value === index)
        return;
    areaIndex.value = index;
    rankStore.initRankList({ dailyTeamRaceId: areaArr[areaIndex.value] });
};
const focusNowArea = () => {
    if (!initData?.value?.dailyTeamRaceId) {
        areaIndex.value = 0;
    }
    else {
        areaIndex.value = areaArr.indexOf(initData?.value?.dailyTeamRaceId);
    }
    console.log('focusNowArea');
};
useEventBus('scroll-to-bottom').on(() => {
    console.log('下一页');
    rankStore.loadMore({ ...(currentTab.value === 'total' ? {} : { dailyTeamRaceId: areaArr[areaIndex.value] }) });
});
onMounted(() => {
    focusNowArea();
});
</script>

<style lang="less" scoped>
.record-box {
    .pic-bg(url('@/assets/img/tab1/<EMAIL>'), 350px, 49px);
    margin: 0 auto;
    position: relative;
    padding-left: 63px;
    font-size: 12px;
    font-weight: normal;
    color: #ffebc7;
    display: flex;
    justify-content: center;
    flex-direction: column;
    p {
        line-height: 20px;
    }
    .btn {
        width: 77px;
        height: 20px;
        position: absolute;
        right: 0;
        top: 0;
        z-index: 2;
    }
}
.reward-box {
    width: 375px;
    height: 119px;
    padding-left: 50px;
    padding-top: 8px;
    p {
        color: #ffebc7;
        font-size: 11px;
    }
    .rewards {
        width: 310px;
        display: flex;
        flex-wrap: nowrap;
        justify-content: flex-start;
        overflow-x: scroll;
        overflow-y: hidden;
        height: 100px;
    }
}
.total_bg {
    .full-bg('@/assets/img/tab1/<EMAIL>');
}
.area-box {
    .pic-bg(url('@/assets/img/tab1/<EMAIL>'), 375px, 26px);
    display: flex;
    justify-content: center;
    align-items: center;
    div {
        width: 60px;
        text-align: center;
        color: #bba6a1;
        font-size: 12px;
    }
    div.active {
        color: #fdff74;
    }
}
.rank-box {
    padding-bottom: 80px;
}
</style>
