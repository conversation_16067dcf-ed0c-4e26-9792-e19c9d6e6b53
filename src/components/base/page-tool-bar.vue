<template>
    <van-floating-bubble
        v-model:offset="offset"
        style="
            width: 100vw;
            height: fit-content;
            z-index: 99999;
            border-radius: 0;
            background: none;
        "
        :gap="0"
        @offset-change="handleOffsetChange">
        <div class="page-tool-bar">
            <div class="server-time">服务器时间：{{ currentServerTime }}</div>
        </div>
    </van-floating-bubble>
</template>

<script setup>
import dayjs from 'dayjs';

const storage = useActivityStorage('tool-bar', { x: 0, y: 0 });
const offset = ref(storage.value);

function handleOffsetChange(obj) {
    storage.value = obj;
}

const currentServerTime = computed(() => {
    if (!serverTime.value)
        return '暂无服务器时间';
    return dayjs.unix(serverTime.value).tzFormat('YYYY-MM-DD HH:mm:ss');
});
</script>

<style lang="less" scoped>
.page-tool-bar {
    width: 375px;
    display: flex;
    flex-wrap: wrap;
    background-color: rgba(0, 0, 0, 0.3);
    color: rgba(255, 255, 255, 0.8);
    .server-time {
        height: 30px;
        flex: 1;
        line-height: 30px;
        text-align: center;
        font-size: 14px;
    }
}
</style>
