<template>
    <popup-container
        v-model:show="isShow"
        :close-on-click-overlay="true"
    >
        <div class="rule-popup">规则页</div>
    </popup-container>
</template>

<script setup>
const isShow = ref(false);

useEventBus('rule-popup').on(({ show = true }) => {
    isShow.value = show;
});
</script>

<style lang="less" scoped>
.rule-popup {
    width: 375px;
    height: 538px;
    padding-top: 38px;
    background-color: #fff;
}
</style>
