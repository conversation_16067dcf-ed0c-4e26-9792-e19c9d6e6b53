<template>
    <div class="activity-tabs z01- relative flex justify-center">
        <div
            v-for="tab in tabList"
            :key="tab.id"
            class="tab-item relative relative z-10 h-[39px] w-[117px] flex items-center justify-center"
            :class="{ active: activeTabId === tab.id }"
            @click="switchTab(tab.id)"
        >
            <img
                :src="activeTabId === tab.id ? tab.activeIcon : tab.inactiveIcon"
                class="h-full w-full"
                alt="" />
        </div>
    </div>
</template>

<script setup>
import { storeToRefs } from 'pinia';
import useActivityTabsStore from '@/stores/modules/use-activity-tabs-store';

const activityTabsStore = useActivityTabsStore();
const { tabList, activeTabId } = storeToRefs(activityTabsStore);
const { switchTab } = activityTabsStore;
</script>

<style scoped lang="less">
.activity-tabs {
    width: 100%;
    .active {
        z-index: 0 !important;
    }
}
</style>
