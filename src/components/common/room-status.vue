<template>
    <div
        v-if="item.channelInfo?.channelId"
        class="room-status"
        alt=""
        @click="handleAvatar(item.channelInfo?.channelId, 2)">
    </div>
</template>

<script setup>
const props = defineProps({
    item: {
        type: Object,
        default() {
            return {};
        },
    },
});
</script>

<style lang="less" scoped>
.room-status {
    position: absolute;
    left: 50%;
    bottom: 0px;
    transform: translateX(-50%);
    width: 41px;
    height: 14px;
    // background: #af4025;
    background: url('@/assets/img/tab3/<EMAIL>');
    background-size: 100% 100%;
    line-height: 13px;
    color: #fff;
    font-size: 9px;
    text-align: center;
}
</style>
