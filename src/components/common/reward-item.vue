<template>
    <div
        class="reward"
        :style="{ width: computedItemWidth, ...computedItemStyle }"
    >
        <div
            class="reward__image"
            :style="{
                width: computedImgWidth,
                height: computedImgWidth,
                backgroundImage: `url(${imgBg})`,
            }"
        >
            <img
                :style="{
                    width: `${imgPercent}%`,
                    height: `${imgPercent}%`,
                }"
                :src="reward?.imageUrl"
                :alt="reward?.name || '奖励图片'"
            />
        </div>
        <div
            class="reward__text"
            :style="computedTextStyle"
        >
            <div
                class="reward__text-primary">
                {{ text1 !== undefined ? text1 : (reward?.[textKey1] || reward?.name) }}
            </div>
            <div
                class="reward__text-secondary">
                {{ text2 !== undefined ? text2 : (reward?.[textKey2] || price || reward?.mark) }}
            </div>
        </div>
        <slot :reward="reward" />
    </div>
</template>

<script setup>
import { getRewardDataInfo } from '@/utils';

// Props 定义
const props = defineProps({
    rewardId: {
        type: String,
        default: '',
    },
    rewardInfo: {
        type: Object,
        default: () => ({}),
    },
    itemWidth: {
        type: String,
        default: '',
    },
    imgWidth: {
        type: String,
        default: '',
    },
    imgBg: {
        type: String,
        default: '',
    },
    textStyle: {
        type: Object,
        default: () => ({}),
    },
    textKey1: {
        type: String,
        default: '',
    },
    textKey2: {
        type: String,
        default: '',
    },
    text1: {
        type: String,
        default: undefined,
    },
    text2: {
        type: String,
        default: undefined,
    },
    itemStyle: {
        type: Object,
        default: () => ({}),
    },
    imgPercent: {
        type: [Number, String],
        default: 80,
    },
});
// 计算属性
const reward = computed(() => ({
    ...getRewardDataInfo(props.rewardId || props.rewardInfo?.id),
    ...props.rewardInfo,
}));

const price = computed(() => {
    return reward.value?.price ? `${reward.value?.price}${reward.value?.package_type === '红钻礼物' ? '红钻' : '豆'}` : '';
});

// 工具函数
function pxToVw(value) {
    if (typeof value !== 'string')
        return value;
    const baseSize = 375;
    const vw = baseSize / 100;
    return value.replace(/(\d+(?:\.\d+)?)px/g, (match, num) => {
        return `${Number(num) / vw}vw`;
    });
}

const getVwStyle = (style) => {
    if (!style)
        return {};
    return Object.entries(style).reduce((acc, [key, value]) => {
        acc[key] = typeof value === 'string' && value.includes('px')
            ? pxToVw(value)
            : value;
        return acc;
    }, {});
};

const computedTextStyle = computed(() => {
    return getVwStyle(props.textStyle);
});

const computedImgWidth = computed(() =>
    props.imgWidth ? pxToVw(props.imgWidth) : '0vw',
);
const computedItemWidth = computed(() =>
    props.itemWidth ? pxToVw(props.itemWidth) : computedImgWidth.value,
);

const computedItemStyle = computed(() => {
    return getVwStyle(props.itemStyle);
});
</script>

<style lang="less" scoped>
.reward {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    height: fit-content;

    &__image {
        position: relative;
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 0;
        overflow: hidden;
        img {
            object-fit: contain;
            will-change: transform;
        }
    }

    &__text {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 100%;
        font-size: 10px;
        text-align: center;
        font-family:
            Alibaba PuHuiTi,
            Alibaba PuHuiTi-65 Medium;
        color: #ffffff;
        &-primary,
        &-secondary {
            white-space: nowrap;
            width: fit-content;
        }
    }
}
</style>
