<template>
    <div
        ref="broadcastRef"
        :class="broadcastClass"
        @touchstart="onBroadcastTouchStart"
        @touchend="onBroadcastTouchend"
        @touchcancel="onBroadcastTouchend">
        <div
            ref="broadcastWrapperRef"
            class="broadcast-wrapper"
            @transitionend.self="onWrapperTransitionend">
            <div
                v-for="(item, i) in broadcastData"
                :key="i"
                ref="broadcastItemsRef"
                class="broadcast-item"
                :class="{
                    'xy-loop-item':
                        i >= props.data.length && ['x', 'y'].includes(direction) && isTouching,
                }"
                :style="broadcastItemStyle">
                <!-- 左侧插槽 -->
                <div
                    v-if="$slots.front"
                    class="broadcast-item-content-front">
                    <slot
                        name="front"
                        :item="item"></slot>
                </div>
                <!-- 内容插槽 -->
                <div
                    ref="broadcastItemsContentRef"
                    v-scroll="onBroadcastItemContentScroll"
                    class="broadcast-item-content"
                    @touchstart="onItemContentTouchStart"
                    @touchend="onItemContentTouchend"
                    @touchcancel="onItemContentTouchend">
                    <div
                        ref="broadcastItemsScrollContentRef"
                        class="broadcast-item-content-scroll-content"
                        @transitionend.self="onItemContentTransitionend">
                        <slot :item="item">
                            <div
                                class="broadcast-item-content-default-element"
                                v-html="item.content"></div>
                        </slot>
                    </div>
                </div>
                <!-- 右侧插槽 -->
                <div
                    v-if="$slots.back"
                    class="broadcast-item-content-back">
                    <slot
                        name="back"
                        :item="item"></slot>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { vScroll } from '@vueuse/components';

const props = defineProps({
    // 滚动方向（x,y,x-y,y-x）
    direction: {
        type: String,
        default: 'x',
    },
    // 滚动模式(loop: 连续不留白、blank: 连续留白、stop: 停顿)，direction为’x’、'y’时生效
    mode: {
        type: String,
        default: 'loop',
    },
    // 滚动速度（px/秒），以设计稿375px为基准
    speed: {
        type: Number,
        default: 30,
    },
    // 延迟开始时间
    delay: {
        type: Number,
        default: 1000,
    },
    // 单项切换过渡时间，direction为’x-y’、‘y-x’或mode为'stop’时生效
    duration: {
        type: Number,
        default: 1000,
    },
    // 单项切换间隔时间
    stopTime: {
        type: Number,
        default: 1000,
    },
    // 允许触摸滑动
    allowTouch: {
        type: Boolean,
        default: false,
    },
    data: {
        type: Array,
        default: () => [],
    },
});

// 延时器
let sleepTimer = null;
// 阻断函数
let rejectFn = null;
// keepAlive 模式下标记
let isFirstActivated = false;
// 连续滚播衔接项总长/高度
let contentsPreviewLength = 0;
// 交叉滚播item索引
let broadcastIndex = 0;
let isActive = true;

const broadcastRef = ref(null);
const broadcastPos = ref({});
const broadcastWrapperRef = ref(null);
const broadcastWrapperPos = ref({});
const broadcastItemsRef = ref([]);
const broadcastItemsContentRef = ref([]);
const broadcastItemsContentPos = ref([]);
const broadcastItemsScrollContentRef = ref([]);
const broadcastItemsScrollContentPos = ref([]);
// 触摸状态
const isTouching = ref(false);
// 连续滚播衔接项
const dataLoop = ref([]);
// scrollLoop scroll事件是否被触发
const { isScrolling: isBroadcastScrolling } = useScroll(broadcastRef);
// scrollCross scroll事件是否被触发
const isBroadcastItemContentScrolling = ref(false);
const onBroadcastItemContentScroll = ({ isScrolling }) =>
    (isBroadcastItemContentScrolling.value = isScrolling.value);

// 是否子项切换过渡中
const isItemTurning = ref(false);

const posField = computed(() => (['x', 'x-y'].includes(props.direction) ? 'width' : 'height'));
const scrollField = computed(() =>
    ['x', 'x-y'].includes(props.direction) ? 'scrollLeft' : 'scrollTop',
);
// 是否展示空白占位
const isBlankShow = computed(
    () => props.mode === 'blank' || (props.mode === 'loop' && !dataLoop.value.length),
);
// 动态渲染数据
const broadcastData = computed(() => {
    if (!props.data || !props.data.length)
        return [];
    let arr = [];
    if (['x', 'y'].includes(props.direction) && props.mode === 'loop' && dataLoop.value.length) {
        arr = dataLoop.value;
    }
    if (
        ['x-y', 'y-x'].includes(props.direction)
        || (props.mode === 'stop' && ['x', 'y'].includes(props.direction))
    ) {
        arr = props.data.slice(0, 1);
    }
    return [...props.data, ...arr];
});
// 速度值换算
const speedCalc = computed(() => (window.innerWidth * props.speed) / 375);
// 是否可触发broadcast的touch事件
const isLoopCanTouch = computed(
    () =>
        props.data
        && props.data.length
        && ['x', 'y'].includes(props.direction)
        && props.allowTouch
        && ((props.mode === 'loop' && dataLoop.value.length)
            || (props.mode === 'blank'
                && broadcastWrapperPos.value[posField.value] > broadcastPos.value[posField.value])),
);
// 是否可触发broadcastItem的touch事件
const isCrossCanTouch = computed(
    () =>
        props.data
        && props.data.length
        && ['x-y', 'y-x'].includes(props.direction)
        && props.allowTouch
        && !isItemTurning.value,
);
// 动态样式
const broadcastClass = computed(() => [
    'broadcast',
    `direction-${props.direction}`,
    ['x', 'y'].includes(props.direction) ? `mode-${props.mode}` : undefined,
    isLoopCanTouch.value ? 'loop-can-touch' : undefined,
    isCrossCanTouch.value ? 'cross-can-touch' : undefined,
]);
// 滚播子项样式
const broadcastItemStyle = computed(() => {
    if (!broadcastRef.value)
        return {};
    if (
        ['x-y', 'y-x'].includes(props.direction)
        || (props.mode === 'stop' && ['x', 'y'].includes(props.direction))
    )
        return ['y-x', 'x'].includes(props.direction)
            ? { width: `${broadcastPos.value.width}px` }
            : { height: `${broadcastPos.value.height}px` };
    return {};
});

// 等待
function sleep(time) {
    clear();
    return new Promise((resolve) => {
        sleepTimer = setTimeout(() => {
            sleepTimer = null;
            // 传false代表可以继续
            resolve(false);
        }, time);
        rejectFn = () => {
            clearTimeout(sleepTimer);
            sleepTimer = null;
            resolve(true);
        };
    });
}

// 阻断操作
function clear(cb) {
    if (rejectFn) {
        rejectFn();
        rejectFn = null;
    }
    cb && cb();
}

// 监听Wrapper元素transionend事件完成连续滚播
function onWrapperTransitionend() {
    if (!isActive)
        return;
    switch (props.direction) {
        // 单向滚播
        case 'x':
        case 'y':
            if (props.mode === 'stop')
                handleCrossTransitionend();
            else handleLoopTransitionend();
            break;
        case 'x-y':
        case 'y-x':
            handleCrossTransitionend();
            break;
        default:
            break;
    }
}

/**
 * ********************************
 * *********  单向滚播 *************
 * ********************************
 */

// 单向滚播
function scrollLoop() {
    if (!isActive)
        return;
    const transform = getComputedStyle(broadcastWrapperRef.value).transform.split(',');

    // 已滚动长度/高度
    const transformOffset
        = props.direction === 'x' ? Number(transform[4]) : Number(transform[5].replace(')', ''));
    // 内容总长/高度
    const contentLength = broadcastWrapperPos.value[posField.value];
    // 移动目标值
    const offsetTarget = isBlankShow.value ? contentLength : contentLength - contentsPreviewLength;
    // 需要移动的距离
    const offset = offsetTarget + transformOffset;
    const offsetTargetPx
        = props.direction === 'x' ? `${-offsetTarget}px, 0` : `0, ${-offsetTarget}px`;
    // 需要移动的时间
    const duration = offset / speedCalc.value;

    broadcastWrapperRef.value.style.transitionDuration = `${duration}s`;
    broadcastWrapperRef.value.style.transform = `translate(${offsetTargetPx})`;
}

// 处理单向滚播的transitionend事件
function handleLoopTransitionend() {
    if (!isActive)
        return;
    if (isBlankShow.value) {
        // 空白模块总长/高度
        const blankLength = broadcastPos.value[posField.value];
        const offsetTargetPx
            = props.direction === 'x' ? `${blankLength}px, 0` : `0, ${blankLength}px`;
        // 从末端位置衔接
        broadcastWrapperRef.value.style.transitionDuration = '0s';
        broadcastWrapperRef.value.style.transform = `translate(${offsetTargetPx})`;
    }
    else {
        // 从开始位置衔接
        broadcastWrapperRef.value.style.transitionDuration = '0s';
        broadcastWrapperRef.value.style.transform = 'translate(0, 0)';
    }
    nextTick().then(scrollLoop);
}

// 处理单向滚播的touch事件
function onBroadcastTouchStart() {
    if (!isLoopCanTouch.value || !isActive)
        return;
    clear();
    if (isTouching.value)
        return;
    isTouching.value = true;

    // 空白模块总长/高度
    const blankLength = broadcastPos.value[posField.value];
    // 滚播内容总长/高度
    const contentLength = broadcastWrapperPos.value[posField.value];
    const transform = getComputedStyle(broadcastWrapperRef.value).transform.split(',');
    // 已滚动长度/高度
    const transformOffset
        = props.direction === 'x' ? Number(transform[4]) : Number(transform[5].replace(')', ''));
    // 从开始位置衔接
    const transformOffsetAbs = Math.abs(transformOffset);

    if (isBlankShow.value) {
        if (transformOffset >= 0) {
            broadcastRef.value[scrollField.value] = 0;
        }
        else {
            const scrollLength = Math.min(transformOffsetAbs, contentLength - blankLength);
            broadcastRef.value[scrollField.value] = scrollLength > 0 ? scrollLength : 0;
        }
    }
    else {
        if (transformOffsetAbs <= contentLength - blankLength - contentsPreviewLength) {
            broadcastRef.value[scrollField.value] = transformOffsetAbs;
        }
        else if (transformOffsetAbs <= contentLength - blankLength - contentsPreviewLength / 2) {
            broadcastRef.value[scrollField.value] = contentLength - contentsPreviewLength;
        }
        else {
            broadcastRef.value[scrollField.value] = 0;
        }
    }

    broadcastWrapperRef.value.style.transitionDuration = '0s';
    broadcastWrapperRef.value.style.transform = 'translate(0, 0)';
}

// 处理单向滚播的touchend事件
async function onBroadcastTouchend() {
    if (!isLoopCanTouch.value || !isActive)
        return;
    if (isBroadcastScrolling.value) {
        watch(isBroadcastScrolling, val => !val && onBroadcastTouchend(), {
            once: true,
        });
        return;
    }
    else if (await sleep(props.stopTime))
        return;

    const translatePx = -broadcastRef.value[scrollField.value];

    broadcastWrapperRef.value.style.transitionDuration = '0s';
    broadcastWrapperRef.value.style.transform = `translate(${props.direction === 'x' ? translatePx : 0}px, ${props.direction === 'y' ? translatePx : 0}px)`;

    broadcastRef.value[scrollField.value] = 0;
    isTouching.value = false;

    nextTick().then(scrollLoop);
}

/**
 * ********************************
 * *********  交叉滚播 *************
 * ********************************
 */

// 交叉滚播(内容)
async function scrollCrossInner() {
    if (!isActive)
        return;
    const contScroll = broadcastItemsScrollContentRef.value[broadcastIndex];
    const transform = getComputedStyle(contScroll).transform.split(',');

    // 已滚动长度/高度
    const transformOffset
        = props.direction === 'x-y' ? Number(transform[4]) : Number(transform[5].replace(')', ''));
    // 滚动容器长/高度
    const scrollLength = broadcastItemsContentPos.value[broadcastIndex][posField.value];
    // 内容总长/高度
    const contentLength = broadcastItemsScrollContentPos.value[broadcastIndex][posField.value];

    if (contentLength <= scrollLength) {
        onItemContentTransitionend();
    }
    else {
        // 移动目标值
        const offsetTarget = contentLength - scrollLength;
        // 需要移动的距离
        const offset = offsetTarget + transformOffset;
        const offsetTargetPx
            = props.direction === 'x-y' ? `${-offsetTarget}px, 0` : `0, ${-offsetTarget}px`;
        // 需要移动的时间
        const duration = offset / speedCalc.value;
        if (offset <= 0) {
            onItemContentTransitionend();
            return;
        }

        contScroll.style.transitionDuration = `${duration}s`;
        contScroll.style.transform = `translate(${offsetTargetPx})`;
    }
}

// 交叉滚播(子项切换)
function scrollCrossOuter() {
    if (!isActive)
        return;
    isItemTurning.value = true;

    // 子项切换要移动的距离
    const offset = broadcastPos.value[['x-y', 'y'].includes(props.direction) ? 'height' : 'width'];
    // 子项已滚动长度/高度
    let scrollValue = broadcastIndex * offset;
    // 滚动目标px值
    const offsetTargetPx = `-${scrollValue + offset}px`;

    broadcastWrapperRef.value.style.transitionDuration = `${props.duration / 1000}s`;
    broadcastWrapperRef.value.style.transform = `translate(${['x-y', 'y'].includes(props.direction) ? 0 : offsetTargetPx}, ${['y-x', 'x'].includes(props.direction) ? 0 : offsetTargetPx})`;
}

// 处理交叉滚播子项切换的transitionend事件
async function handleCrossTransitionend() {
    if (!isActive)
        return;
    isItemTurning.value = false;

    // 重置滚动结束的上一个子项状态
    if (['x-y', 'y-x'].includes(props.direction)) {
        const contScroll = broadcastItemsScrollContentRef.value[broadcastIndex];
        contScroll.style.transitionDuration = '0s';
        contScroll.style.transform = 'translate(0, 0)';
    }
    // 递增当前子项索引
    broadcastIndex = broadcastIndex + 1 >= props.data.length ? 0 : broadcastIndex + 1;
    if (broadcastIndex === 0) {
        broadcastWrapperRef.value.style.transitionDuration = '0s';
        broadcastWrapperRef.value.style.transform = 'translate(0, 0)';
    }

    if (await sleep(props.stopTime))
        return;
    if (['x', 'y'].includes(props.direction) && props.mode === 'stop') {
        scrollCrossOuter();
        return;
    }
    scrollCrossInner();
}

// 监听Item元素transionend事件完成交叉滚播子项内容滚播
async function onItemContentTransitionend() {
    if (!isActive)
        return;
    if (await sleep(props.stopTime))
        return;
    scrollCrossOuter();
}

// 处理交叉滚播的touch事件
function onItemContentTouchStart() {
    if (!isCrossCanTouch.value || !isActive)
        return;
    clear();
    if (isTouching.value)
        return;
    isTouching.value = true;

    const contwrap = broadcastItemsContentRef.value[broadcastIndex];
    const contScroll = broadcastItemsScrollContentRef.value[broadcastIndex];
    const transform = getComputedStyle(contScroll).transform.split(',');
    // 已滚动长度/高度
    const transformOffset
        = props.direction === 'x-y' ? Number(transform[4]) : Number(transform[5].replace(')', ''));
    // 从开始位置衔接
    const transformOffsetAbs = Math.abs(transformOffset);
    contwrap[scrollField.value] = transformOffsetAbs;

    contScroll.style.transitionDuration = '0s';
    contScroll.style.transform = 'translate(0, 0)';
}

// 处理交叉滚播的touchend事件
async function onItemContentTouchend() {
    if (!isCrossCanTouch.value || !isActive)
        return;
    const contwrap = broadcastItemsContentRef.value[broadcastIndex];
    const contScroll = broadcastItemsScrollContentRef.value[broadcastIndex];
    if (isBroadcastItemContentScrolling.value) {
        watch(isBroadcastItemContentScrolling, val => !val && onItemContentTouchend(), {
            once: true,
        });
        return;
    }
    else if (await sleep(props.stopTime))
        return;

    const translatePx = -contwrap[scrollField.value];

    contScroll.style.transitionDuration = '0s';
    contScroll.style.transform = `translate(${props.direction === 'x-y' ? translatePx : 0}px, ${props.direction === 'y-x' ? translatePx : 0}px)`;

    contwrap[scrollField.value] = 0;
    isTouching.value = false;

    nextTick().then(scrollCrossInner);
}

// 状态重置
function reset() {
    clear();
    broadcastIndex = 0;
    if (broadcastRef.value) {
        broadcastWrapperRef.value.style.transitionDuration = '0s';
        broadcastWrapperRef.value.style.transform = 'translate(0, 0)';
    }
    if (broadcastItemsScrollContentRef.value.length) {
        broadcastItemsScrollContentRef.value.forEach((el) => {
            el.style.transitionDuration = '0s';
            el.style.transform = 'translate(0, 0)';
        });
    }
}

// 初始化
async function init() {
    if (!isActive)
        return;
    broadcastPos.value = broadcastRef.value.getBoundingClientRect();
    await nextTick();
    // 算出需要几个子项才能撑开无缝滚播
    if (['x', 'y'].includes(props.direction) && props.mode === 'loop') {
        // dataLoop子项总长/高度
        contentsPreviewLength = 0;
        const itemLoop = [];
        for (let i = 0; i < broadcastItemsRef.value.length; i++) {
            const el = broadcastItemsRef.value[i];
            const elPos = el.getBoundingClientRect();
            contentsPreviewLength += elPos[posField.value];
            itemLoop.push(props.data[i]);
            if (contentsPreviewLength >= broadcastPos.value[posField.value])
                break;
        }
        if (contentsPreviewLength >= broadcastPos.value[posField.value])
            dataLoop.value = itemLoop;
    }
    await nextTick();
    broadcastWrapperPos.value = broadcastWrapperRef.value.getBoundingClientRect();
    broadcastItemsContentPos.value = broadcastItemsContentRef.value.map(el =>
        el.getBoundingClientRect(),
    );
    broadcastItemsScrollContentPos.value = broadcastItemsScrollContentRef.value.map(el =>
        el.getBoundingClientRect(),
    );
}

// 开始滚播
function play() {
    if (!isActive)
        return;
    if (!props.data || props.data.length === 0)
        return;
    switch (props.direction) {
        // 单向滚播
        case 'x':
        case 'y':
            if (props.mode === 'stop')
                scrollCrossOuter();
            else scrollLoop();
            break;
        // xy交叉滚播
        case 'x-y':
        case 'y-x':
            scrollCrossInner();
            break;
        default:
            break;
    }
}

watch(
    () => props.data,
    async () => {
        reset();
        await nextTick();
        await init();
        if (await sleep(props.delay))
            return;
        play();
    },
);

onMounted(async () => {
    isActive = true;
    await init();
    if (await sleep(props.delay))
        return;
    play();
});
onUnmounted(() => clear(() => (isActive = false)));

onActivated(async () => {
    isActive = true;
    if (!isFirstActivated)
        return (isFirstActivated = true);
    reset();
    await init();
    if (await sleep(props.delay))
        return;
    play();
});
onDeactivated(() => clear(() => (isActive = false)));
</script>

<style lang="less" scoped>
.broadcast {
    width: 100%;
    height: 100%;
    overflow-x: hidden;
    overflow-y: hidden;
    .broadcast-wrapper {
        display: flex;
        flex-shrink: 0;
        transform: translate(0, 0);
        transition-duration: 0s;
        transition-timing-function: linear;
        transition-property: transform;
        .broadcast-item {
            display: flex;
            flex-shrink: 0;
            .broadcast-item-content-front,
            .broadcast-item-content-back,
            .broadcast-item-content {
                flex-shrink: 0;
            }
            .broadcast-item-content {
                flex-grow: 1;
                .broadcast-item-content-scroll-content {
                    transform: translate(0, 0);
                    transition-duration: 0s;
                    transition-timing-function: linear;
                    transition-property: transform;
                    .broadcast-item-content-default-element {
                        width: max-content;
                    }
                }
            }
        }
    }
    &.direction-x {
        &.loop-can-touch {
            overflow-x: auto;
        }
        &.mode-stop {
            overflow-x: hidden;
            .broadcast-item-content {
                flex: 1;
                overflow: hidden;
                .broadcast-item-content-scroll-content {
                    width: 100%;
                    .broadcast-item-content-default-element {
                        width: 100%;
                        overflow: hidden;
                        white-space: nowrap;
                        text-overflow: ellipsis;
                    }
                }
            }
        }
        .broadcast-wrapper {
            width: max-content;
            height: 100%;
            .xy-loop-item {
                width: 0;
                overflow: hidden;
            }
            .broadcast-item-content-scroll-content {
                width: max-content;
                height: 100%;
            }
        }
    }
    &.direction-y {
        &.loop-can-touch {
            overflow-y: auto;
        }
        &.mode-stop {
            overflow-y: hidden;
            .broadcast-item-content {
                flex: 1;
                overflow: hidden;
                .broadcast-item-content-scroll-content {
                    height: 100%;
                }
            }
        }
        .broadcast-wrapper {
            flex-direction: column;
            width: 100%;
            // height: max-content;
            .broadcast-item {
                flex-direction: column;
            }
            .xy-loop-item {
                height: 0;
                overflow: hidden;
            }
        }
    }
    &.direction-x-y {
        &.cross-can-touch {
            .broadcast-wrapper .broadcast-item .broadcast-item-content {
                overflow-x: auto;
            }
        }
        .broadcast-wrapper {
            flex-direction: column;
            width: 100%;
            height: max-content;
            .broadcast-item {
                width: 100%;
                .broadcast-item-content {
                    flex: 1;
                    overflow-x: hidden;
                    overflow-y: hidden;
                    white-space: nowrap;
                    .broadcast-item-content-scroll-content {
                        width: max-content;
                        height: 100%;
                    }
                }
            }
        }
    }
    &.direction-y-x {
        &.cross-can-touch {
            .broadcast-wrapper .broadcast-item .broadcast-item-content {
                overflow-y: auto;
            }
        }
        .broadcast-wrapper {
            width: max-content;
            height: 100%;
            .broadcast-item {
                flex-direction: column;
                height: 100%;
                .broadcast-item-content {
                    flex: 1;
                    overflow-x: hidden;
                    overflow-y: hidden;
                }
            }
        }
    }
}
</style>
