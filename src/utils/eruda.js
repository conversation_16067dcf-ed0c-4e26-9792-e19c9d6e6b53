import { loadScriptSync } from '.';

export default async function initEruda() {
    await loadScriptSync(requirePublic('/eruda@3.4.1_1.js'));
    window.eruda.init({
        tool: ['console', 'info', 'network', 'sources', 'elements', 'resources', 'snippets'],
        useShadowDom: false,
        defaults: {
            displaySize: 80,
        },
    });
    document.body.addEventListener('click', () => {
        if (window.eruda)
            window.eruda?.hide?.();
    });
}
