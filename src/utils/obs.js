import { formatTime } from '.';
import config from '@/config';
import { obsDomain } from '@/config/url';

/**
 * obs上传
 * @param {object} obj
 * @param {file} obj.file 文件对象
 * @param {string} obj.key 文件路径名称
 * @param {string} obj.token obs token
 * @returns {Promise<[{location: string, hash: string}]>} 返回文件信息 location: 文件链接, hash: 文件hash值
 */
export const uploadObsFile = ({ file, key, token }) => {
    return new Promise((resolve) => {
        if (!key) {
            let ext = file.name.lastIndexOf('.');
            ext = ext !== -1 ? file.name.substring(ext + 1) : '';
            // 生成文件路径
            key = `web/${config.projectName}/${formatTime(Date.now() / 1000, 'yyyyMMddhhmmss_S')}${Math.random().toString().substring(2, 5)}.${ext}`;
        }
        const data = new FormData();
        data.append('key', key);
        data.append('file', file);

        const xhr = new XMLHttpRequest();

        xhr.onload = () => {
            try {
                const resposnse = {
                    ...JSON.parse(xhr.responseText),
                    hash: xhr.getResponseHeader('etag'), // 从响应头获取文件hash值
                };
                resolve([resposnse]);
            }
            catch (error) {
                resolve([undefined, error]);
            }
        };

        xhr.onerror = () => {
            resolve([undefined, xhr.responseText]);
        };

        xhr.open('post', obsDomain);
        xhr.setRequestHeader('Authorization', `OBS ${token}`);
        xhr.send(data);
    });
};
