import { track } from './jsbridge';

// 图片懒加载
const LazyLoad = {
    // install方法
    install(app, options) {
        // 代替图片的loading图
        const defaultSrc = options.default;
        app.directive('lazy', {
            created(el, binding) {
                LazyLoad.init(el, binding.value, defaultSrc);
            },
            mounted(el) {
                // 兼容处理
                if ('IntersectionObserver' in window)
                    LazyLoad.observe(el);
                else LazyLoad.listenerScroll(el);
            },
        });
    },
    // 初始化
    init(el, val, def) {
        // data-src 储存真实src
        el.setAttribute('data-src', val);
        el.setAttribute('src', def);
    },
    // 利用IntersectionObserver监听el
    observe(el) {
        const io = new IntersectionObserver((entries) => {
            const realSrc = el.dataset.src;
            if (entries[0].isIntersecting) {
                if (realSrc) {
                    el.src = realSrc;
                    el.removeAttribute('data-src');
                }
            }
        });
        io.observe(el);
    },
    // 监听scroll事件
    listenerScroll(el) {
        const handler = LazyLoad.throttle(LazyLoad.load, 300);
        LazyLoad.load(el);
        window.addEventListener('scroll', () => {
            handler(el);
        });
    },
    // 加载图片
    load(el) {
        const windowHeight = document.documentElement.clientHeight;
        const elTop = el.getBoundingClientRect().top;
        const elBtm = el.getBoundingClientRect().bottom;
        const realSrc = el.dataset.src;
        if (elTop - windowHeight < 0 && elBtm > 0) {
            if (realSrc) {
                el.src = realSrc;
                el.removeAttribute('data-src');
            }
        }
    },
    // 节流
    throttle(fn, delay) {
        let timer;
        let prevTime;
        return function (...args) {
            const currTime = Date.now();
            if (!prevTime)
                prevTime = currTime;
            clearTimeout(timer);

            if (currTime - prevTime > delay) {
                prevTime = currTime;
                fn.apply(this, args);
                clearTimeout(timer);
                return;
            }

            timer = setTimeout(function () {
                prevTime = Date.now();
                timer = null;
                fn.apply(this, args);
            }, delay);
        };
    },
};

export default {
    install(app) {
        LazyLoad.install(app, {
            default:
                'https://obs-cdn.52tt.com/tt/fe-moss/web/loading/20230421174650_95985446.gif', // 默认图片封面
        });

        // 节流
        app.directive('throttle', {
            created: (el, binding) => {
                let throttleTime = binding.value;
                if (!throttleTime)
                    throttleTime = 2000;
                let cbFun;
                el.addEventListener(
                    'click',
                    (event) => {
                        if (!cbFun) {
                            // 第一次执行
                            cbFun = setTimeout(() => {
                                cbFun = null;
                            }, throttleTime);
                        }
                        else {
                            event && event.stopImmediatePropagation();
                        }
                    },
                    true,
                );
            },
        });

        // 复制
        app.directive('copy', {
            created(el, { value }) {
                el.$value = value;
                el.handler = () => {
                    if (!el.$value) {
                        // eslint-disable-next-line no-console
                        console.log('无复制内容');
                        return;
                    }
                    const textarea = document.createElement('textarea');
                    textarea.readOnly = true;
                    textarea.style.position = 'absolute';
                    textarea.style.left = '-9999px';
                    textarea.value = el.$value;
                    document.body.appendChild(textarea);
                    textarea.select();
                    const result = document.execCommand('Copy');
                    if (result)
                        // eslint-disable-next-line no-console
                        console.log('复制成功，复制内容为：', el.$value);

                    document.body.removeChild(textarea);
                };
                el.addEventListener('click', el.handler);
            },
            componentUpdated: (el, { value }) => {
                el.$value = value;
            },
            unbind: (el) => {
                el.removeEventListener('click', el.handler);
            },
        });

        // 拖拽
        app.directive('drag', {
            mounted: (el) => {
                el.ontouchstart = function (e) {
                    const disX = e.touches[0].pageX - el.offsetLeft;
                    const disY = e.touches[0].pageY - el.offsetTop;
                    document.ontouchmove = function (e) {
                        let x = e.touches[0].pageX - disX;
                        let y = e.touches[0].pageY - disY;
                        const maxX
                            = document.body.clientWidth
                            - Number.parseInt(window.getComputedStyle(el).width);
                        const maxY
                            = document.body.clientHeight
                            - Number.parseInt(window.getComputedStyle(el).height);

                        if (x < 0)
                            x = 0;
                        if (x > maxX)
                            x = maxX;
                        if (y < 0)
                            y = 0;
                        if (y > maxY)
                            y = maxY;

                        el.style.position = 'fixed';
                        el.style.left = `${x}px`;
                        el.style.top = `${y}px`;
                    };
                    document.ontouchend = function () {
                        document.ontouchmove = document.ontouchend = null;
                    };
                };
            },
        });

        // 埋点
        app.directive('track', {
            created: (el, binding) => {
                const { value = {}, modifiers, arg = '' } = binding;
                const { exposure = false, delay = false } = modifiers;
                if (!arg) {
                    console.warn('=>请检查埋点参数');
                    return;
                }
                if (exposure) {
                    // 点击事件埋点
                    setTimeout(
                        () => {
                            track(
                                {
                                    page_id: arg || 'activity_page',
                                },
                                'exposure',
                            );
                        },
                        delay ? 2000 : 0,
                    );
                }
                else {
                    // 曝光埋点
                    el.addEventListener('click', () => {
                        setTimeout(
                            () => {
                                track(
                                    {
                                        page_id: arg || 'activity_page',
                                        ...value,
                                    },
                                    'click',
                                );
                            },
                            delay ? 1000 : 0,
                        );
                    });
                }
            },
        });

        // 无限滚动加载
        app.directive('scrollBottom', {
            created: (el, binding) => {
                el.onscroll = () => {
                    let isTrigger = false;
                    if (el.scrollHeight - el.clientHeight <= el.scrollTop) {
                        if (isTrigger)
                            return;
                        isTrigger = true;
                        setTimeout(() => {
                            binding.value();
                            isTrigger = false;
                        }, 500);
                    }
                };
            },
        });
    },
};
