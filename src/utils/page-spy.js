import { loadScriptSync } from '.';
import config from '@/config';

// page-spy服务地址
const PAGE_SPY_API = 'testing-tt-web-tc.ttyuyin.com/common-testing/page-spy-api';
// 项目id
const PROJECT_ID = config.projectName;

export default async function initPageSpy() {
    // 读取缓存中的设备信息
    const psFlag = parse(localStorage.getItem('page-spy-room'));
    const hasPsFlag
        = psFlag && typeof psFlag === 'object' && psFlag?.roomUrl?.includes?.(PAGE_SPY_API);
    if (hasPsFlag)
        sessionStorage.setItem('page-spy-room', stringify(psFlag));
    // 加载page-spy相关js
    await loadScriptSync([
        `https://${PAGE_SPY_API}/page-spy/index.min.js`,
        `https://${PAGE_SPY_API}/plugin/data-harbor/index.min.js`,
        `https://${PAGE_SPY_API}/plugin/rrweb/index.min.js`,
    ]);
    window.$harbor = new DataHarborPlugin();
    window.$rrweb = new RRWebPlugin();
    [window.$harbor, window.$rrweb].forEach((p) => {
        PageSpy.registerPlugin(p);
    });

    window.$pageSpy = new PageSpy({
        // 项目id
        project: PROJECT_ID,
        // 筛选参数，可自定义如填uid
        title: `uid: ${myWebview.params.uid}`,
        api: PAGE_SPY_API,
        clientOrigin: `https://${PAGE_SPY_API}`,
        // 是否序列化控制台输出的对象类型数据
        serializeData: true,
        primaryColor: '#bcdffb',
        logo: 'https://obs-cdn.52tt.com/tt/fe-moss/web/user-report-2024/20250117105608_83010980.png',
        modal: {
            title: `页面操作日志记录`,
            logo: 'https://obs-cdn.52tt.com/tt/fe-moss/web/user-report-2024/20250117105608_83010980.png',
        },
        // 对数据进行过滤，返回false则不记录该数据
        dataProcessor: {
            console: (data) => {
                // 打印内容中如果有 "report data2" 字符则忽略（apm-sdk的数据打印）
                if (data.logs.some(i => typeof i === 'string' && i.includes('report data2')))
                    return false;
            },
            network: (data) => {
                // 忽略apm-sdk类的请求
                if (/test-cloud-ingest-all-gz\.skyengine\.com\.cn\/api\/apm_notauth/.test(data.url))
                    return false;
            },
        },
    });
    // 缓存设备信息
    if (!hasPsFlag) {
        setTimeout(() => {
            const psFlagS = sessionStorage.getItem('page-spy-room');
            if (psFlagS)
                localStorage.setItem('page-spy-room', psFlagS);
        }, 500);
    }
}
