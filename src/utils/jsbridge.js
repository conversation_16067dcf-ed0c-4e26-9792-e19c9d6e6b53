import BylinkSDK from '@tt/bylink-sdk';
import config from '../config';
import { afterVersion, filterParams, params2search } from './index';
import { jsBridgePreview } from '@/config/url';
import { env, nodeUrl } from '@/config/url';

const bylink = new BylinkSDK(config.projectName, false);

/**
 * @name getMyAccount 获取ttid
 */
export const getMyAccount = () => {
    if (myWebview.isInApp()) {
        try {
            return TTJSBridge.invoke('data', 'getMyAccount');
        }
        catch {
            return '';
        }
    }
    return '';
};

/**
 * @name getToken 获取token
 */
export const getToken = () => {
    if (myWebview.isInApp()) {
        try {
            return TTJSBridge.invoke('data', 'getToken');
        }
        catch {
            return '';
        }
    }
    return '';
};

/**
 * @name toRoom 跳转房间
 * @param {string} channel_id 房间号
 */
export const toRoom = (channel_id) => {
    console.log('toRoom', channel_id);
    if (myWebview.isInApp()) {
        try {
            if (!channel_id)
                return;
            // 房间跟随状态点击上报，榜单通用且强制上报规则
            bylink.track({
                page_id: 'activity_rank_template_page',
                event_id: 'activity_rank_template_enter_room_click',
                general_param_1: JSON.stringify({
                    target_uid: parseUrlQuery()?.uid || 0,
                    room_id: channel_id,
                }),
            }, 'click');
            TTJSBridge.invoke('operate', 'jump', `${jsBridgePreview}/channel?channel_id=${channel_id}`);
        }
        catch {}
    }
};

/**
 * @name toPerson 跳转用户信息
 * @param {string} account 账户
 */
export const toPerson = (account) => {
    console.log('toPerson', account);
    if (myWebview.isInApp()) {
        try {
            if (!account)
                return;
            // 头像点击上报，榜单通用且强制上报规则
            bylink.track({
                page_id: 'activity_rank_template_page',
                event_id: 'activity_rank_template_click',
                general_param_1: JSON.stringify({
                    target_uid: parseUrlQuery()?.uid || 0,
                }),
            }, 'click');
            TTJSBridge.invoke('operate', 'jump', `${jsBridgePreview}/userdetail?account=${account}`);
        }
        catch {}
    }
};

/**
 * @name toGuild 跳转公会信息
 * @param {string} id 公会ID
 */
export const toGuild = (id) => {
    if (myWebview.isInApp()) {
        try {
            if (!id)
                return;
            TTJSBridge.invoke(
                'operate',
                'jump',
                `${jsBridgePreview}/guild_card?guild_id=${id}&hy_guild_id=${id}&mk_guild_id=${id}`,
            );
        }
        catch {
            // console.log('toGuild', e);
        }
    }
};

/**
 * @name jumpLink 跳转链接
 * @param {string} link 链接
 */
export const jumpLink = (link) => {
    if (myWebview.isInApp()) {
        try {
            TTJSBridge.invoke('operate', 'jump', link);
        }
        catch {}
    }
};

/**
 * @name jumpUrl 跳转内部短链接
 * @param {string} url 内部短链接
 */
export const jumpUrl = (url) => {
    if (myWebview.isInApp()) {
        try {
            TTJSBridge.invoke('operate', 'jump', `${jsBridgePreview}/${url}`);
        }
        catch {}
    }
};

/**
 * @name jumpIm 跳转到IM
 * @param {number} account 账户
 */
export const jumpIm = (account) => {
    if (myWebview.isInApp()) {
        try {
            TTJSBridge.invoke('operate', 'jump', `${jsBridgePreview}/chat?account=${account}`);
        }
        catch {}
    }
};

/**
 * @name setRightTextVisibility 是否显示顶部bar栏右边文字
 * @param {boolean} $boolean
 */
export const setRightTextVisibility = ($boolean) => {
    if (myWebview.isInApp()) {
        try {
            TTJSBridge.invoke('ui', 'setRightTextVisibility', $boolean);
        }
        catch {}
    }
};

/**
 * @name updateRightItemView 设置顶部bar栏右边文字&样式
 * @param {string} text 文字
 * @param {string} color 字体颜色
 * @param {string} bgColor 背景颜色
 */
export const updateRightItemView = (text, color, bgColor) => {
    if (myWebview.isInApp) {
        try {
            const json = { text, textColor: color, text_bg_color: bgColor };

            TTJSBridge.invoke('ui', 'updateRightItemView', JSON.stringify(json));
        }
        catch {
            // alert(e);
        }
    }
};

/**
 * @name updateRightText 设置顶部bar栏右边文字
 * @param {string} text
 */
export const updateRightText = (text) => {
    if (myWebview.isInApp()) {
        try {
            TTJSBridge.invoke('ui', 'updateRightText', text);
        }
        catch {
            // alert(e);
        }
    }
};

/**
 * @name setRightTextRunMethod 设置顶部bar栏右边文字点击方法回调
 * @param {string} method 回调方法
 */
export const setRightTextRunMethod = (method = '') => {
    if (myWebview.isInApp()) {
        try {
            TTJSBridge.invoke('ui', 'setRightTextRunMethod', `{"method" : ${method}}`);
        }
        catch {
            // alert(e);
        }
    }
};

/**
 * @name setRightButtonList 隐藏右上角三个点
 */
export const setRightButtonList = (list) => {
    if (myWebview.isInApp()) {
        try {
            TTJSBridge.invoke('ui', 'setRightButtonList', JSON.stringify(list));
        }
        catch {
            // alert(e);
        }
    }
};

/**
 * @name updateTitleBarMode 修改顶部title样式
 * @param {boolean} $boolean true:白色 false:黑色
 */
export const updateTitleBarMode = ($boolean) => {
    if (myWebview.isInApp) {
        try {
            TTJSBridge.invoke('ui', 'updateTitleBarMode', $boolean);
        }
        catch {
            // alert(e);
        }
    }
};

/**
 * @name setGoBackInvokeMethod 修改返回按钮方法
 * @param {string} methodName 方法名 em：myWebview.share
 * @param {Array} args 参数数组
 */
export const setGoBackInvokeMethod = (methodName, args = []) => {
    if (myWebview.isInApp) {
        try {
            TTJSBridge.invoke(
                'ui',
                'setGoBackInvokeMethod',
                JSON.stringify({
                    methodKey: location.origin + location.pathname,
                    method: methodName,
                    params: [...args],
                }),
            );
        }
        catch {
            // alert(e);
        }
    }
};

/**
 * @name updateBarColor 设置顶部bar背景色
 * @param {string} color 颜色
 */
export const updateBarColor = (color) => {
    if (myWebview.isInApp) {
        try {
            TTJSBridge.invoke('ui', 'updateBarColor', color); // 顶部 bar背景色
        }
        catch {
            // alert(e);
        }
    }
};

export const showToast = (parameters) => {
    if (myWebview.isInApp()) {
        try {
            TTJSBridge.invoke('operate', 'showToast', parameters);
        }
        catch {
            // webErrorReport('apiError', 'Show toast failed - ' + e.name + ": " + e.message);
        }
    }
};

/**
 * @name getDeviceId 获取设备号
 */
export const getDeviceId = () => {
    let deviceId;
    if (myWebview.isInApp()) {
        try {
            deviceId = TTJSBridge.invoke('data', 'getDeviceId');
        }
        catch {
            // alert(e);
        }
    }
    return deviceId;
};

/**
 * @name toKaihei 跳转去开黑
 */
export const toKaihei = () => {
    if (myWebview.isInApp()) {
        try {
            TTJSBridge.invoke('operate', 'jump', `${jsBridgePreview}/home?main_tab=channel&second_tab=sec_gang_up`);
        }
        catch {
            // alert(e)
        }
    }
};

/**
 * @name toEquip 跳转到个性装扮
 * @param {number} selectTab 0 =>麦位框 1 =>坐骑 2 =>主页飘
 */
export const toEquip = (selectTab) => {
    if (myWebview.isInApp()) {
        try {
            TTJSBridge.invoke('operate', 'jump', `${jsBridgePreview}/userPersonalityDress?select_tab=${selectTab}`);
        }
        catch {
            // alert(e)
        }
    }
};

/**
 * @name toChange // 跳转到充值页
 */
export const toChange = () => {
    if (myWebview.isInApp()) {
        try {
            TTJSBridge.invoke('operate', 'jump', `${jsBridgePreview}/tcoin`);
        }
        catch {
            // alert(e)
        }
    }
};

/**
 * @name saveImg 保存到相册
 * @param {base64} base64 图片
 * @param {Function} cb 回调函数
 */
export const saveImg = (base64, cb) => {
    if (myWebview.isInApp()) {
        try {
            TTJSBridge.invoke('operate', 'saveImgToGallery', JSON.stringify({ base64, imgType: 2 }), cb);
        }
        catch {}
    }
};

/**
 * @name toPublishActivity 进入发布页
 * @param {object} param 参数
 */
export const toPublishActivity = (param = {}) => {
    if (myWebview.isInApp()) {
        try {
            TTJSBridge.invoke(
                'operate',
                'toPublishActivity',
                JSON.stringify({
                    pageType: myWebview.isIOS() ? 2 : 5,
                    ...param,
                }),
            );
        }
        catch {
            // alert(JSON.stringify(e));
        }
    }
};

/**
 * @name getVersion 获取App版本号
 */
export const getVersion = () => {
    if (myWebview.isInApp()) {
        try {
            return TTJSBridge.invoke('operate', 'getVersion');
        }
        catch {}
    }
};

/**
 * @name specificVersion 是否版本以上
 * @param {number} phoneType 0:安卓 1:iOS
 * @param {Array} specificVersion [安卓, ios] 例如['67108874', '4.0.9']
 * @param {Function} cb 回调函数
 */
export const specificVersion = (phoneType, specificVersion, cb) => {
    if (myWebview.isInApp()) {
        try {
            const cuVer = getVersion();
            const version = specificVersion;

            if (cuVer && phoneType && cuVer < version[phoneType])
                showToast('当前版本不支持该操作，请升级最新版本');
            else if (cb)
                cb();
        }
        catch {
            // alert('specificVersion:' + JSON.stringify(e))
        }
    }
};

/**
 * @name checkInstallApp 检查是否安装APP
 * @param {string} appName Wechat | QQ | QZone
 * @returns {boolean} 是否安装
 */
export const checkInstallApp = (appName) => {
    if (myWebview.isInApp()) {
        try {
            return TTJSBridge.invoke('operate', 'checkInstallApp', appName) === 'true';
        }
        catch {
            return false;
        }
    }
    return false;
};

/**
 * @name finishActivity 关闭webview
 */
export const finishActivity = () => {
    if (myWebview.isInApp()) {
        try {
            return TTJSBridge.invoke('ui', 'finishActivity');
        }
        catch {}
    }
};

/**
 * @name onBackPressed webview回退上一页
 */
export const onBackPressed = () => {
    if (myWebview.isInApp()) {
        try {
            return TTJSBridge.invoke('ui', 'onBackPressed');
        }
        catch {}
    }
};

/**
 * @name enterFullScreen 进入全屏模式
 * @param {boolean} $boolean
 */
export const enterFullScreen = ($boolean) => {
    if (myWebview.isInApp()) {
        try {
            TTJSBridge.invoke('ui', 'enterfullscreen', $boolean);
        }
        catch {
            //
        }
    }
};

/**
 * @name pageView 页面曝光上报
 * @param {string} pageId
 * @param {string} source
 */
export const pageView = (pageId, source = '') => {
    bylink.pageView(pageId, source);
};

/**
 * @typedef {object} ActivityProperties 活动配置
 * @property {string} activity_id 活动id 为空值时，以activity_name为准
 * @property {string} activity_type 活动类型 1-常规活动   2-个性化运营活动
 * @property {string} activity_name 活动名称
 * @property {string} activity_url 完整活动链接，带参数
 * @property {string} page_ext 活动链接，不带参数
 * @property {string} app_type 标识不同app，tt语音-ttvoice，在呀-zaiya，欢游-huanyou
 */

/**
 * @typedef {object} ActivityPageProperties 活动页面配置
 * @property {string} page_id 页面id
 * @property {string} first_element_id 事件ID，与event_id相同
 * @property {string} event_id 事件ID，与first_element_id相同
 * @property {string} second_element_id 子事件ID，与event_ext相同
 * @property {string} event_ext 子事件ID，与second_element_id相同
 * @property {string} general_param_1 业务参数
 * @property {string} invited_uid 邀请人uid
 * @property {string} tt_uid :端内邀请人ttid
 * @property {string} source 来源
 * @property {string} cp_id 渠道包id
 * @property {string} ad_type 广告类型 默认0，裂变广告为1
 * @property {number} uid uid
 * @property {string} uuid uuid
 */

/**
 * @name track 统计上报事件
 * @param {ActivityProperties & ActivityPageProperties} properties
 * @param {'click' | 'exposure'} event
 */
export const track = (properties, event = 'click') => {
    bylink.track(properties, event);
};

export const pushUrlParams = (obj = {}) => {
    history.replaceState(
        {},
        '',
        location.origin + location.pathname + params2search(Object.assign({}, myWebview.params, obj)),
    );
};

export const filterUrlParams = (list = []) => {
    history.replaceState(
        {},
        '',
        location.origin + location.pathname + params2search(filterParams(myWebview.params, list)),
    );
};

export const shareFriend = (uid) => {
    if (myWebview.isInApp()) {
        const locationUrl = `${window.location.protocol}//${window.location.host}${window.location.pathname}`;
        const shareData = {
            share_type: 'TT',
            ttShareMsgType: 0,
            title: '快来和我组CP吧～',
            content: '快来和我组CP吧～',
            url: `${locationUrl}?cpuid=${uid}`,
            imageUrl: `https://ga-album-cdnqn.52tt.com/web/anniversary-guild-match-2020/20201215173655_56339602.png`, // 图文里的缩略图url
            imagePath: '', // 纯图片类型（仅支持手机本地地址）
            label: '', // 入口标签名
            musicUrl: '',
        };
        try {
            TTJSBridge.invoke('ui', 'thirdPlatformShare', JSON.stringify(shareData));
        }
        catch {}
    }
};

/**
 * @name getCurrentChannelInfo 获取当前房间信息
 */
export const getCurrentChannelInfo = () => {
    if (myWebview.isInApp()) {
        try {
            return TTJSBridge.invoke('data', 'getCurrentChannelInfo');
        }
        catch {
            return null;
        }
    }
    return null;
};

/**
 * @name jumpRandomRoom // 快速进房
 */
export const jumpRandomRoom = () => {
    const ramdomNum = [2013, 2010, 2001, 2002, 2014, 2012, 2007, 2005, 3000];
    const ramdomId = ramdomNum[Math.floor(Math.random() * ramdomNum.length)];
    if (myWebview.isInApp()) {
        try {
            TTJSBridge.invoke(
                'operate',
                'jump',
                `${jsBridgePreview}/entQuickEntry?tag_id=${ramdomId}&tab_id=${ramdomId}`,
            );
        }
        catch {
            // alert(e)
        }
    }
};

/**
 * @name jumpRandomLive // 快速进直播房
 */
export const jumpRandomLive = () => {
    if (myWebview.isInApp()) {
        try {
            TTJSBridge.invoke('operate', 'jump', `${jsBridgePreview}/entQuickEntry?tag_id=3000&tab_id=3000`);
        }
        catch {
            // alert(e)
        }
    }
};

/**
 * @name getWechatInfo 获取授权
 */
export const getWechatInfo = (method = '') => {
    if (myWebview.isInApp()) {
        try {
            if (!checkInstallApp('Wechat')) {
                showToast('未安装微信，无法绑定');
                return;
            }
            TTJSBridge.invoke('operate', 'getWeChatInfo', '', method);
        }
        catch {}
    }
};

/**
 * @name sendImageToIM 发送图片到im
 * @param {string} imgPath 设备本地图片地址
 */
export const sendImageToIM = (imgPath) => {
    try {
        const shareData = {
            share_type: 'TT',
            ttShareMsgType: 1, // 1图片，其他（图文）0 默认
            imagePath: imgPath, // 纯图片类型（仅支持手机本地地址）
        };
        TTJSBridge.invoke('ui', 'thirdPlatformShare', JSON.stringify(shareData)); // 新接口
    }
    catch (error) {
        showToast(`发送失败：${error.toString()}`);
    }
};

/**
 * @name saveImgByBase64 保存图片到本地
 * @param {string} imageBase64 imageBase64
 * @returns {Promise<string>} imgPath 设备本地图片路径
 */
export const saveImgByBase64 = (imageBase64) => {
    return new Promise((resolve) => {
        let toast = true; // 超时标志位
        if (myWebview.isInApp()) {
            myWebview.downloadPic = async (dataStr) => {
                const result = JSON.parse(dataStr);
                const { imgPath } = result;
                if (!imgPath) {
                    toast = false; // 不设置超时提示
                    resolve();
                    showToast('保存图片失败');
                }
                else {
                    toast = false; // 不设置超时提示
                    resolve(imgPath);
                }
            };
            try {
                // 处理ios第一次访问授权未关闭弹窗
                setTimeout(() => {
                    if (toast) {
                        resolve();
                        showToast('保存超时，请重试');
                    }
                }, 7000);
                TTJSBridge.invoke(
                    'operate',
                    'saveImgToGallery',
                    JSON.stringify({
                        base64: imageBase64,
                        imgType: 1,
                    }),
                    'myWebview.downloadPic',
                ); // 新接口
            }
            catch {
                resolve();
                showToast('保存图片失败');
            }
        }
        else {
            resolve();
            showToast('端外不可以操作');
        }
    });
};

/**
 * @name remindCalendarActivity 添加日历提醒
 */
export const remindCalendarActivity = (params = {}, method = '') => {
    if (myWebview.isInApp()) {
        try {
            let api = 'remindCalendarActivity';
            if (!myWebview.isIOS()) {
                if (!afterVersion('6.12.0'))
                    api = 'operateCalendarEvent';
                // 移除日历提醒
                // 安卓根据eventIdentifier删除，ios根据eventIdentifier与系统自动生成的id映射关系删除(用户卸载app后无法移除日历提醒)
                TTJSBridge.invoke(
                    'operate',
                    api,
                    JSON.stringify({
                        actionStyle: 2,
                        title: params.title,
                        startDate: params.startDate,
                        endDate: params.endDate,
                        notes: params.notes,
                        eventIdentifier: params.eventIdentifier,
                    }),
                );
            }
            // 写入日历
            TTJSBridge.invoke(
                'operate',
                api,
                JSON.stringify({
                    actionStyle: 1,
                    ...params,
                }),
                method,
            );
        }
        catch {}
    }
};

/**
 * @name deleteCalendarActivity 删除日历提醒
 */
export const deleteCalendarActivity = (params = {}, method = '') => {
    if (myWebview.isInApp()) {
        try {
            let api = 'remindCalendarActivity';
            if (!afterVersion('6.12.0') && !myWebview.isIOS())
                api = 'operateCalendarEvent';
            // 移除日历提醒
            // 安卓根据eventIdentifier删除，ios根据eventIdentifier与系统自动生成的id映射关系删除(用户卸载app后无法移除日历提醒)
            TTJSBridge.invoke(
                'operate',
                api,
                JSON.stringify({
                    actionStyle: 2,
                    ...params,
                }),
                method,
            );
        }
        catch {}
    }
};

/**
 * @name getAnchorUid 获取当前auid
 */
export const getAnchorUid = () => {
    if (myWebview.isInApp()) {
        try {
            let anchorInfo = TTJSBridge.invoke('data', 'getCurrentChannelInfo');
            try {
                if (typeof anchorInfo === 'string') {
                    anchorInfo = JSON.parse(anchorInfo);
                }
            }
            catch {
                return 0;
            }
            if (anchorInfo?.type === 7) {
                return anchorInfo?.creator_uid || 0;
            }
        }
        catch {
            return 0;
        }
    }
    else if (env !== 'prod') {
        console.log('myWebview.params.aUid', myWebview.params.aUid);

        return myWebview.params.aUid;
    }
    return 0;
};