/**
 * 分享方法：
 * 1、长链转短链
 * 2、生成口令码
 * 3、同步复制口令码和短链
 * 4、端外链接诱导分享风控检测
 */
import { getToken } from './jsbridge';
import { copyToClip } from '@/utils';
import { createAxios } from '@/utils/request';
import { curUrl } from '@/config/url';
import { urlConfig } from '@/config/url-config';
import config from '@/config';

// 后台服务请求域名
const osType = myWebview.isIOS() ? 'ios' : 'android';
const baseURL = urlConfig[`node_common_${osType}`].prod;

const request = createAxios({ baseURL });

const fetchApi = ({ api, data = {}, config = {} }) => request.post(api, data, config);

/**
 * @name getShortLink 长链转短链
 * @param {string} url 长链链接 默认值:活动链接
 * @returns {Promise<string>} link 短链链接
 */
export const getShortLink = async (url = curUrl) => {
    try {
        const { data } = await fetchApi({
            api: 'short-link/shortlink.System/encode',
            data: {
                url,
                type: 1,
                expired_time: 0,
            },
        });
        if (data && data.link) {
            return data.link;
        }
        return null;
    }
    catch {
        return null;
    }
};

/**
 * @name createPsw 生成口令
 * @param {string} url 长链链接 默认值:活动链接
 * @returns {Promise<string>} code 口令码
 */
export const createPsw = async (url = curUrl) => {
    try {
        const { data } = await fetchApi({
            api: 'webpsw/pwd.Pwd/create',
            data: {
                url,
                headImg: '',
                title: '',
                codeDesc: '',
            },
        });
        if (data && data.content) {
            return data.content;
        }
        return null;
    }
    catch {
        return null;
    }
};

/**
 * @name copyPswAndShortLink 复制口令和短链 移动端浏览器限制，复制命令不能在异步代码中执行
 * @param {object} obj 参数对象
 * @param {string} obj.url 长链链接 默认值:活动链接
 * @param {string} obj.reg 复制文案，可替换 {psw} 为口令码，可替换 {link} 为短链
 * @param {boolean} obj.withPsw 是否生成口令
 * @param {boolean} obj.withShortLink 是否生成短链
 */
export const copyPswAndShortLink = ({
    url = curUrl,
    reg = '{psw}',
    withPsw = true,
    withShortLink = false,
} = {}) => {
    return new Promise((resolve) => {
        let xmlHttpPsw;
        let xmlHttpShortLink;
        let returnVal = reg;
        let psw = '';
        let shortLink = '';

        try {
            if (withPsw) {
                xmlHttpPsw = new window.XMLHttpRequest();
                xmlHttpPsw.open('POST', `${baseURL}webpsw/pwd.Pwd/create`, false);
                xmlHttpPsw.setRequestHeader('Content-type', 'application/json;charset=UTF-8');
                xmlHttpPsw.send(
                    JSON.stringify({
                        url,
                        headImg: '',
                        title: '',
                        codeDesc: '',
                    }),
                );
                const resPsw = xmlHttpPsw.responseText;
                if (resPsw.length) {
                    const data = JSON.parse(resPsw);
                    if (data?.data?.content) {
                        returnVal = returnVal.replace(/\{psw\}/g, data.data.content);
                        psw = data.data.content;
                    }
                    else {
                        resolve({ code: 1, msg: data?.data?.message || '生成口令失败' });
                    }
                }
            }
            if (withShortLink) {
                xmlHttpShortLink = new window.XMLHttpRequest();
                xmlHttpShortLink.open(
                    'POST',
                    `${baseURL}short-link/shortlink.System/encode`,
                    false,
                );
                xmlHttpShortLink.setRequestHeader('Content-type', 'application/json;charset=UTF-8');
                xmlHttpShortLink.setRequestHeader('Authorization', getToken());
                xmlHttpShortLink.send(
                    JSON.stringify({
                        token: getToken(),
                        url,
                        type: 1,
                        expired_time: 0,
                    }),
                );
                const resShortLink = xmlHttpShortLink.responseText;
                if (resShortLink.length) {
                    const data = JSON.parse(resShortLink);
                    if (data?.data?.link) {
                        returnVal = returnVal.replace(/\{link\}/g, data.data.link);
                        shortLink = data.data.link;
                    }
                    else {
                        resolve({ code: 1, msg: data?.data?.message || '生成口令失败' });
                    }
                }
            }
            resolve({
                code: 0,
                data: {
                    psw,
                    shortLink,
                    copyValue: copyToClip(returnVal),
                },
            });
        }
        catch {
            resolve({ code: 1, msg: '生成口令失败' });
        }
    });
};

/**
 * @name getShareUrl 端外链接诱导分享风控检测
 * @description 可获取新分享链接绕开封控 https://gitlab.52tt.com/frontend/nodejs/common/weixin-bridge-api
 * @returns {Promise<string>} url
 */
export const getShareUrl = async () => {
    try {
        // 获取链接被封状态
        const { data: data1 } = await fetchApi({
            api: 'weixin-bridge-api/bridgeApi.WeixinBridgeApi/getLinkState',
            data: { link: curUrl },
        });
        if (data1 && data1.state === 110) {
            // 获取被封新链接
            const { data, code } = await fetchApi({
                api: 'weixin-bridge-api/bridgeApi.WeixinBridgeApi/getShareLink',
                data: {
                    name: config.projectName,
                },
            });
            if (code === 0 && data && data.link)
                return data.link;
            else return curUrl;
        }
        return curUrl;
    }
    catch {
        return curUrl;
    }
};
