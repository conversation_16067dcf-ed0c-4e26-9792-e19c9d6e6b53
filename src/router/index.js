import { createRouter, createWebHashHistory } from 'vue-router';
import { handleHotUpdate, routes } from 'vue-router/auto-routes';
import useRouteTransitionStore from '@/stores/modules/use-route-transition-store';
import useRouteCacheStore from '@/stores/modules/use-route-cache-store';

const router = createRouter({
    history: createWebHashHistory(),
    routes,
});

// This will update routes at runtime without reloading the page
if (import.meta.hot) {
    handleHotUpdate(router);
}

router.beforeEach((to, from, next) => {
    const routeCacheStore = useRouteCacheStore();
    const routeTransitionStore = useRouteTransitionStore();

    // Route cache
    routeCacheStore.addRoute(to);
    // 标记页面触达时间，用于判断前进后退
    if (!to.query._t) {
        to.query._t = new Date().getTime().toString();
        next(to);
        return;
    }
    // 首页加载去除动画
    if (!from.name) {
        routeTransitionStore.setDirection('none');
        next();
        return;
    }
    if (to.query._no_trans) {
        routeTransitionStore.setDirection('none');
        next();
        return;
    }

    if (+to.query._t > +from.query._t)
        routeTransitionStore.setDirection('forward');
    else routeTransitionStore.setDirection('back');

    next();
});

router.afterEach(() => {
    // 修改标题
    window.document.title = router.currentRoute.value.meta.title || '首页';
    if (myWebview.isInApp()) {
        try {
            TTJSBridge.invoke(
                'ui',
                'setCurrentPageTitle',
                JSON.stringify({ title: window.document.title }),
            );
        }
        catch {}
    }
});

export default router;
