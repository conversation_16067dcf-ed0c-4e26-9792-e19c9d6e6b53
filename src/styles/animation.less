// 从下往上
.a-toUp {
    animation: au 0.3s cubic-bezier(0, 1.2, 1, 1) forwards;
    transform: translate3D(0, 0, 0);
    animation-delay: var(--delay-s);
    transform: translateY(40px);
    opacity: 0;
    @keyframes au {
        0% {
            transform: scale(0.7);
            opacity: 0;
            transform: translateY(40px);
        }
        100% {
            transform: scale(1);
            opacity: 1;
            transform: translateY(0);
        }
    }
}

// 从右往左
.a-toLeft {
    animation: al 0.5s cubic-bezier(0, 1.2, 1, 1) forwards;
    animation-delay: var(--delay-s);
    // transform: translateX(90%);
    opacity: 0;
    @keyframes al {
        0% {
            transform: scale(0.92);
            opacity: 0.1;
            transform: translateX(90%);
        }
        100% {
            transform: scale(1);
            opacity: 1;
            transform: translateX(0);
        }
    }
}
// 从左往右
.a-toRight {
    animation: ar 0.5s cubic-bezier(0, 1.2, 1, 1) forwards;
    animation-delay: var(--delay-s);
    transform: translateX(-90%);
    opacity: 0;
    @keyframes ar {
        0% {
            transform: scale(0.92);
            opacity: 0.1;
            transform: translateX(-90%);
        }
        100% {
            transform: scale(1);
            opacity: 1;
            transform: translateX(0);
        }
    }
}
// 从左往右 弹簧
.a-toRightS {
    animation: ars 0.5s cubic-bezier(0.5, 1.2, 0.75, 1) forwards;
    animation-delay: var(--delay-s);
    transform: translateX(-90%);
    opacity: 0;
    @keyframes ars {
        0% {
            transform: scale(0.92);
            opacity: 0.1;
            transform: translateX(-90%);
        }
        75% {
            transform: scale(1);
            opacity: 1;
            transform: translateX(10px);
        }
        100% {
            transform: scale(1);
            opacity: 1;
            transform: translateX(0);
        }
    }
}
// 从右往左 弹簧
.a-toLeftS {
    animation: als 0.5s cubic-bezier(0.5, 1.2, 0.75, 1) forwards;
    animation-delay: var(--delay-s);
    transform: translateX(90%);
    opacity: 0;
    @keyframes als {
        0% {
            transform: scale(0.92);
            opacity: 0.1;
            transform: translateX(90%);
        }
        75% {
            transform: scale(1);
            opacity: 1;
            transform: translateX(-10px);
        }
        100% {
            transform: scale(1);
            opacity: 1;
            transform: translateX(0);
        }
    }
}

// x轴
.a-rotateX {
    animation: arx 2s cubic-bezier(0, 1.2, 1, 1) forwards;
    animation-delay: var(--delay-s);
    transform-origin: center top;
    transform-style: preserve-3d;
    // perspective: 800px;
    transform: perspective(600px) rotateX(45deg);
    position: relative;
    .shadow {
        content: '';
        position: absolute;
        margin: auto;
        left: 0;
        right: 0;
        bottom: 0;
        height: 20px;
        background: #141313;
        z-index: 1;
        transform-origin: 50% 100%;
        animation: sh 2s cubic-bezier(0, 1.2, 1, 1) forwards;
        animation-delay: var(--delay-s);
        transform: rotateX(-220deg);
        @keyframes arx {
            0% {
                opacity: 1;
            }
            10% {
                opacity: 1;
            }
            100% {
                opacity: 0;
            }
        }
    }
    @keyframes rx {
        0% {
            transform: perspective(600px) rotateX(45deg);
        }
        100% {
            transform: perspective(0px) rotateX(0deg);
        }
    }
}

// 高度
.a-height {
    animation: mh 0.5s cubic-bezier(0, 1.2, 1, 1) forwards;
    animation-delay: var(--delay-s);
    opacity: 0;
    transform: scaleY(0.52);
    @keyframes mh {
        0% {
            transform: scaleY(0.52);
            opacity: 0;
        }
        100% {
            transform: scaleY(1);
            opacity: 1;
        }
    }
}

// 大小
.a-scale {
    animation: as 0.5s cubic-bezier(0, 1.2, 1, 1) forwards;
    animation-delay: var(--delay-s);
    opacity: 0;
    @keyframes as {
        0% {
            transform: scale(0.2);
            opacity: 0;
        }
        100% {
            transform: scale(1);
            opacity: 1;
        }
    }
}
