@vw: 3.75vw;
@txt-color: #ffdc98;
@border-width-base: 1px;
@border-color: #ebedf0;

.hairline-common() {
    position: absolute;
    box-sizing: border-box;
    content: ' ';
    pointer-events: none;
}

.hairline(@color: @border-color) {
    .hairline-common();

    top: -50%;
    right: -50%;
    bottom: -50%;
    left: -50%;
    border: 0 solid @color;
    transform: scale(0.5);
}

.hairline-bottom(@color: @border-color, @left: 0) {
    .hairline-common();

    right: 0;
    bottom: 0;
    left: @left;
    border-bottom: 1px solid @color;
    transform: scaleY(0.5);
}

//超过显示省略号
.one-line() {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.one-line {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}
//换行
.tag-norwap(@height:35px) {
    word-break: break-all;
    white-space: normal;
    max-height: @height;
    overflow-y: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
}

.absolute-center() {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    margin: auto;
}

.flex-center() {
    display: flex;
    justify-content: center;
    align-items: center;
}
.flex-center {
    .flex-center();
}
.flex-center-c {
    flex-direction: column;
    .flex-center();
}

.text-gradient(@gradient) {
    background: @gradient;
    -webkit-background-clip: text;
    color: transparent;
}
.gradient(@color1,@color2) {
    background: linear-gradient(-80deg, @color1, @color2);
    background: -webkit-linear-gradient(-80deg, @color1, @color2);
}
.translate3d() {
    transform: translate3d(0, 0, 0);
    -webkit-transform: translate3d(0, 0, 0);
}
/*ready go动画*/
.go(@count) when (@count > 0) {
    &:nth-of-type(@{count}) {
        animation-delay: -0.1s * @count;
    }
}

/** .9图 vw单位 */
.point-9-vw(@url,@width,@height,@top,@right,@bottom,@left,@multiple) {
    width: (@width - @right - @left) / @vw;
    height: (@height - @top - @bottom) / @vw;
    border-top: @top / @vw solid #000;
    border-right: @right / @vw solid #000;
    border-bottom: @bottom / @vw solid #000;
    border-left: @left / @vw solid #000;
    border-image-source: @url;
    border-image-slice: (@top* @multiple) (@right* @multiple) (@bottom* @multiple) (@left* @multiple) fill;
    border-image-width: (@top / @vw) (@right / @vw) (@bottom / @vw) (@left / @vw);
    border-image-repeat: repeat;
}
/** .9图 px单位 */
.point-9-px(@url,@width,@height,@top,@right,@bottom,@left,@multiple) {
    width: (@width - @right - @left) + 0px;
    height: (@height - @top - @bottom) + 0px;
    border-top: @top + 0px solid #000;
    border-right: @right + 0px solid #000;
    border-bottom: @bottom + 0px solid #000;
    border-left: @left + 0px solid #000;
    border-image-source: @url;
    border-image-slice: (@top* @multiple) (@right* @multiple) (@bottom* @multiple) (@left* @multiple) fill;
    border-image-width: @top + 0px @right + 0px @bottom + 0px @left + 0px;
    border-image-repeat: repeat;
}
/** .9图 竖向延展 */
.point-9-vert(@url,@top,@bottom,@repeat:repeat,@multiple:2) {
    border-image-slice: (@top* @multiple) 100% (@bottom* @multiple) 0 fill;
    border-image-width: (@top*1px) 100% (@bottom*1px) 0;
    border-image-repeat: @repeat;
    border-image-source: @url;
    border-width: 0;
}
/** .9图 横向延展 */
.point-9-hori(@url,@left,@right,@repeat:repeat,@multiple:2) {
    border-image-slice: 100% (@right* @multiple) 0 (@left* @multiple) fill;
    border-image-width: 100% (@right*1px) 0 (@left*1px);
    border-image-repeat: @repeat;
    border-image-source: @url;
    border-width: 0;
}

.pic-bg(@url, @width, @height) {
    width: @width;
    height: @height;
    background: @url no-repeat;
    background-size: 100% 100%;
}

.breath {
    animation: s 0.8s linear infinite;
    @keyframes s {
        0% {
            transform: scale(1);
        }
        25% {
            transform: scale(1.05);
        }
        50% {
            transform: scale(1.1);
        }
        75% {
            transform: scale(1.05);
        }
        100% {
            transform: scale(1);
        }
    }
}

.multi-ellipsis(@lines) {
    display: -webkit-box;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-line-clamp: @lines;

    /* autoprefixer: ignore next */
    -webkit-box-orient: vertical;
}

.sheet-backdrop.backdrop-in,
.popup-backdrop.backdrop-in,
.dialog-backdrop.backdrop-in {
    background: rgba(0, 0, 0, 0.8);
}

.full-bg(@url) {
    background: url(@url) no-repeat center;
    background-size: 100% 100%;
}

.user-head(@width) {
    width: @width;
    height: @width;
    border-radius: 50%;
}

.bg-default {
    background-size: 100% 100%;
    background-repeat: no-repeat;
}

.border-rounded {
    border-width: 1px;
    border-radius: 50%;
    box-sizing: border-box;
}
