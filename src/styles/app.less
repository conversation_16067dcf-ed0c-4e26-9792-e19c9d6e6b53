@import './animation.less';
@import './font-face.less';
@import './transition.less';
@import './vant-override.less';

*,
*::before,
*::after {
    box-sizing: border-box;
    outline: none;
}
html {
    background: var(--van-gray-1);
    color-scheme: light;
}
body {
    height: 100%;
    overflow: hidden;
}
p {
    margin: 0;
}
img {
    display: block;
}
ul {
    list-style: none;
}
li {
    list-style-type: none;
}

#app {
    position: relative;
    height: 100%;
    overflow-x: hidden;
}

::-webkit-scrollbar {
    display: none;
    width: 0;
    background: transparent;
}

// 1px边框
[class*='mf-hairline'] {
    &::after {
        .hairline();
    }
}
.mf-hairline {
    &,
    &_top,
    &_right,
    &_bottom,
    &_left,
    &_surround,
    &_top-bottom {
        position: relative;
    }

    &_top::after {
        border-top-width: @border-width-base;
    }
    &_right::after {
        border-right-width: @border-width-base;
    }
    &_left::after {
        border-left-width: @border-width-base;
    }
    &_bottom::after {
        border-bottom-width: @border-width-base;
    }

    &,
    &-unset {
        &_top-bottom::after {
            border-width: @border-width-base 0;
        }
    }

    &_surround::after {
        border-width: @border-width-base;
    }
}

.mf-multi-ellipsis--l2 {
    .multi-ellipsis(2);
}
.mf-multi-ellipsis--l3 {
    .multi-ellipsis(3);
}

// 麦位框
.head-frame {
    z-index: 1;
    // position: absolute;
    // top: 50%;
    // left: 50%;
    width: 90px;
    height: 93px;
    transform-origin: 0 0;
    background-size: cover;
    background-position: 0 0;
    animation: head-frame 1s steps(5) infinite;
    // -webkit-animation: head-frame 1s steps(6) infinite;
}

@head-bg-height: 93px; //31的倍数，否则动图会左右摇晃

@keyframes head-frame {
    0% {
        background-position: 0 0;
    }

    100% {
        background-position: -120vw 0;
    }
}
