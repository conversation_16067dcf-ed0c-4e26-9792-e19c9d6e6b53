import { jumpLink, toGuild, toPerson, toRoom } from '@/utils/jsbridge';
import {
    formatTime,
    getAvatar,
    getByteLen,
    num2percent,
    omitTxt,
    omitValue,
    safeOmitTxt,
    sliceByte,
    supplementDouble,
} from '@/utils';
import config from '@/config';
import { env } from '@/config/url';

/**
 * @param { Promise } promise
 * @param {object=} errorExt - Additional Information you can pass to the err object
 * @return { Promise }
 */
export async function to(promise, errorExt) {
    try {
        const data = await promise;
        return [data, null];
    }
    catch (err) {
        if (errorExt) {
            const parsedError = Object.assign({}, err, errorExt);
            return [{ code: 1 }, parsedError];
        }
        return [{ code: 1 }, err];
    }
}

export function handleAvatar(account, type = 1) {
    if (!account)
        return;
    switch (type) {
        case 1:
            toPerson(account);
            break;
        case 2:
            toRoom(account);
            break;
        case 3:
            toGuild(account);
            break;
        default:
            break;
    }
}

export function avatarError(event) {
    event.target.src = requireImg('default_avatar_no_compress.png');
}

/**
 * 将基于375px设计稿的像素值转换为当前设备的像素值
 * @param {number} px - 设计稿中的像素值
 * @returns {number} - 转换后的像素值
 */
export function calcPx(px) {
    if (px === 0)
        return px;
    const designWidth = 375;
    const currentWidth = window.innerWidth;
    return (px / designWidth) * currentWidth;
}

/**
 * 将基于375px设计稿的像素值转换为当前设备的vw值
 * @param {number} px - 设计稿中的像素值
 * @returns {string} - 转换后的vw值
 */
export function calcVw(px) {
    if (px === 0)
        return px;
    const designWidth = 375;
    const vwN = Math.round(`${(px * 100) / designWidth}e${3}`) / 10 ** 3;
    return `${vwN}vw`;
}

export function stringify(val) {
    if (typeof val === 'string')
        return val;
    else {
        try {
            const valS = JSON.stringify(val);
            return valS;
        }
        catch {
            return '';
        }
    }
}

export function parse(val) {
    try {
        const valP = JSON.parse(val);
        return valP;
    }
    catch {
        return val;
    }
}

/**
 * 以活动id、uid、环境变量作为key，存储数据
 * @param {string} key - 键
 * @param {any} defaultValue - 默认值
 * @param {object} config - 配置参数
 * @param {boolean} config.withUid - 是否带uid
 * @param {boolean} config.withEnv - 是否带env
 * @returns  - ref实例
 */
export function useActivityStorage(
    key,
    defaultValue = null,
    { withUid, withEnv } = { withUid: true, withEnv: true },
) {
    // 缓存key值，区分项目、环境、uid
    const storageKey = `${config.projectName}${withUid ? `-${myWebview.params.uid}` : ''}${
        withEnv ? `-${env}` : ''
    }-${key}`;
    const value = parse(localStorage.getItem(storageKey));
    if (value === null)
        localStorage.setItem(storageKey, stringify(defaultValue));

    // 创建ref对象，存储数据
    const storageValue = ref(value === null ? defaultValue : value);
    // 监听数据变化，更新localStorage
    watch(
        storageValue,
        (val) => {
            localStorage.setItem(storageKey, stringify(val));
        },
        { deep: true },
    );

    return storageValue;
}

/**
 * 多余字符替换为省略号
 * @param {string} str - 文本
 * @param {num} length - 截取长度
 */
export function truncate(str, length) {
    if (!str)
        return '';
    if (length < 2)
        length = 2;
    return getByteLen(str) > length ? `${sliceByte(str, length - 2)}...` : str;
}
export function jumpToLink(activityId) {
    let domainProdPath = 'https://appcdn.52tt.com';
    let url = '';
    if (MARKET_ID === MARKET_ID_MAP.TT) {
        domainProdPath = 'https://appcdn.52tt.com';
    }
    else if (MARKET_ID === MARKET_ID_MAP.HY) {
        if (myWebview.isIOS()) {
            domainProdPath = 'https://appcdn.tses.net';
        }
        else domainProdPath = 'https://zy-appcdn.rzhushou.com';
    }
    else if (MARKET_ID === MARKET_ID_MAP.MK) {
        if (myWebview.isIOS()) {
            domainProdPath = 'https://appcdn.tingyou.fun';
        }
        else domainProdPath = 'https://appcdn.yuyue27.com';
    }
    url = `${domainProdPath}${activityId}`;
    if (myWebview.isInApp()) {
        jumpLink(url);
    }
    else {
        window.location.href = url;
    }
}
export {
    toGuild,
    toPerson,
    toRoom,
    formatTime,
    getAvatar,
    num2percent,
    omitTxt,
    omitValue,
    supplementDouble,
    safeOmitTxt,
};
