/**
 * @name parseUrlQuery 获取url参数
 * @param {string} url
 * @returns {object} query
 */
export const parseUrlQuery = (url = window.location.href) => {
    let uid = '';
    try {
        uid = TTJSBridge.invoke('data', 'getMyUid');
    }
    catch {}

    const result = /\?([^#]*)#?/.exec(url);
    if (!result || !result[1])
        return { uid };
    let paramsStr = result[1]; // 将 ? 后面的字符串提出来
    paramsStr = paramsStr.replace(/#/g, '');
    const paramsArr = paramsStr.split('&');
    const theRequest = paramsArr.reduce((pre, param) => {
        if (/=/.test(param)) {
            const splitIdx = param.indexOf('='); // 切割 key 和 value
            const key = param.substring(0, splitIdx);
            let val = param.substring(splitIdx + 1);
            val = decodeURIComponent(val); // url解码
            val = /^\d+$/.test(val) ? Number.parseFloat(val) : val; // 判断是否是数字
            pre[key] = val; // 默认取同名 key 的最后一个 value
        }
        else {
            if (param)
                pre[param] = true; // 处理没有 value 的参数
        }
        return pre;
    }, {});
    theRequest.uid = theRequest.uid || uid;

    return theRequest;
};

export const myWebview = {
    params: parseUrlQuery(window.location.href),

    // 判断是否在微信内
    isInWx() {
        const ua = navigator.userAgent.toLowerCase();
        return !!ua.match(/MicroMessenger/i);
    },

    // 判断是在qq内
    isInQq() {
        const ua = navigator.userAgent.toLowerCase();
        return !!ua.match(/ qq\//i); // qq的userAgent的前面有个空格，后面有个斜杆
    },

    // 是否在APP内
    isInApp() {
        try {
            TTJSBridge.invoke('operate', 'isInApp');
            return true;
        }
        catch {
            return false;
        }
    },

    // 是否在SDK内
    isInSdk() {
        if (
            Object.prototype.hasOwnProperty.call(this.params, 'app')
            && Number.parseInt(this.params.app, 10) === 12
        )
            return true;
        else return false;
    },

    // 是否IOS
    isIOS() {
        if (Object.prototype.hasOwnProperty.call(this.params, 'os_type')) {
            return Number.parseInt(this.params.os_type, 10) === 2;
        }
        else {
            const ua = navigator.userAgent;
            return !!ua.match(/(iPhone\sOS|iOS)\s([\d_]+)/i);
        }
    },

    // 是否在PC
    isInPC() {
        const userAgent = navigator.userAgent;
        if (
            /Mobile|Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini|Windows Phone/i.test(
                userAgent,
            )
        )
            return false;
        return true;
    },
};

// 马甲包market_id映射
export const MARKET_ID_MAP = {
    TT: 0, // TT
    HY: 2, // 欢游
    MK: 5, // 麦可
    MJ: 6, // 谜境
};

// 马甲包market_id
export const MARKET_ID = Object.values(MARKET_ID_MAP).includes(
    Number.parseInt(myWebview.params.market_id, 10),
)
    ? Number.parseInt(myWebview.params.market_id, 10)
    : MARKET_ID_MAP.TT;
