/**
 * @name requireImg 动态引入图片
 * @param {string} name
 * @returns {string} url
 */
export const requireImg = (name) => {
    return new URL(`../assets/img/${name}`, import.meta.url).href;
};

/**
 * @name requireAssets 动态引入资源
 * @param {string} name
 * @returns {string} url
 */
export const requireAssets = (name) => {
    return new URL(`../assets/${name}`, import.meta.url).href;
};

/**
 * @name requirePublic 动态引入Public资源
 * @param {string} name
 * @returns {string} url
 * @example requirePublic('/apm-sdk.js')
 */
export const requirePublic = (name) => {
    const pathname = location.pathname.match(/(.*)\//)?.[1] || '';
    return `${location.origin}${pathname}${name}`;
};
