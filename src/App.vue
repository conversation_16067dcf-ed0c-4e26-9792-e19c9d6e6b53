<template>
    <van-config-provider
        class="root-app"
        theme="light">
        <router-view v-slot="{ Component, route }">
            <transition :name="routeTransitionStore.routeTransitionName">
                <keep-alive :include="routeCache.routeCaches">
                    <component
                        :is="Component"
                        :key="route.name" />
                </keep-alive>
            </transition>
        </router-view>
    </van-config-provider>
    <!-- 下线弹窗 -->
    <activity-end-modal />
    <!-- 页面工具栏 -->
    <page-tool-bar v-if="env !== 'prod'" />
    <!-- 分享弹窗 -->
    <share-modal />
</template>

<script setup>
import config from './config';
import { env } from './config/url';
import useRouteCacheStore from './stores/modules/use-route-cache-store';
import useRouteTransitionStore from './stores/modules/use-route-transition-store';

const routeTransitionStore = useRouteTransitionStore();
const routeCache = useRouteCacheStore();

onMounted(() => {
    if (config.__isOffline.startsWith('____ActivityOfflineTag____')) {
        // 打开活动结束弹窗
        useEventBus('activity-end-modal').emit({ show: true });
    }
});
</script>

<style lang="less" scoped>
.root-app {
    position: relative;
    width: 100%;
    height: 100%;
}
</style>
