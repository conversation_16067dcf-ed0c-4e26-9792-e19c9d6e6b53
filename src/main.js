import { createApp } from 'vue';
import mockjs from 'mockjs?url';
import dayjs from 'dayjs';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';
import tzFormat from 'dayjs-tz-format';
import App from './App.vue';
import { env } from './config/url';
import directive from './utils/directive';
import { loadScriptSync } from './utils';
// import initPageSpy from './utils/page-spy';
import initEruda from './utils/eruda';
import router from '@/router';
import pinia from '@/stores';
// import 'virtual:uno.css';
import 'normalize.css/normalize.css';
import '@/styles/app.less';
// Vant 桌面端适配
// import '@vant/touch-emulator';

// 启用时区插件
dayjs.extend(timezone);
dayjs.extend(utc);
dayjs.tz.setDefault('Asia/Shanghai');
dayjs.extend(tzFormat);

allowMultipleToast();
// 全局赋值
window.myWebview = myWebview;
window.MARKET_ID = MARKET_ID;

const main = async () => {
    try {
        const { mock, novconsole } = myWebview.params;
        if (myWebview.isInApp()) {
            TTJSBridge.invoke('ui', 'setRightButtonList', JSON.stringify([3])); // 只保留右上角刷新
        }
        // 测试&灰度环境提前加载vconsole
        if (env !== 'prod' && !novconsole && !myWebview.isInPC()) {
            await initEruda();
        }
        if (mock) {
            // 加载mockjs
            await loadScriptSync(mockjs);
        }
        // page-spy加载
        // if (env !== 'prod') {
        //     await initPageSpy();
        // }
    }
    catch {}

    document.getElementById('app').classList.remove('__skeleton');
    const app = createApp(App);
    app.use(router);
    app.use(pinia);
    app.use(directive);
    app.mount('#app');
};

main();
