/**
 * 获取进度条百分比和当前任务索引
 * @param {number} value 当前值
 * @param {Array} taskConfigList 任务列表
 * @param {Array} percentLocationList 百分比位置列表
 * @param {HTMLElement} progressListRef 进度条DOM引用
 * @param {boolean} isVertical 是否垂直方向
 * @returns {{percent: string, index: number}} 进度条百分比和当前任务索引
 */
function getProgress(
    value,
    taskConfigList,
    percentLocationList,
    progressListRef,
    isVertical = false,
) {
    // 参数校验 - 保护子句
    if (
        !progressListRef
        || value == null
        || !taskConfigList?.length
        || !percentLocationList?.length
    ) {
        return { percent: '0%', index: 0 };
    }

    // 获取进度条总长度
    const progressSize = isVertical ? progressListRef.clientHeight : progressListRef.clientWidth;

    if (!progressSize) {
        return { percent: '0%', index: 0 };
    }

    // 找到当前值所在的任务区间
    const targetIndex = taskConfigList.findIndex(task => task.value > value);

    // 如果当前值超过所有任务值,返回100%
    if (targetIndex === -1) {
        return { percent: '100%', index: taskConfigList.length - 1 };
    }

    // 计算进度长度
    const prevTask = targetIndex > 0 ? taskConfigList[targetIndex - 1] : null;
    const curTask = taskConfigList[targetIndex];

    // 计算已完成部分的进度长度
    const progressLength = targetIndex > 0 ? percentLocationList[targetIndex - 1] : 0;

    // 计算当前任务的进度
    const prevValue = prevTask?.value || 0;
    const prevValueGap = value - prevValue;
    const curValuePercent = prevValueGap / (curTask.value - prevValue);

    // 计算当前任务对应的进度条增量
    const curLengthGap = percentLocationList[targetIndex] - progressLength;
    const totalLength = progressLength + curValuePercent * curLengthGap;

    const finalPercent = Math.min((totalLength * 100) / progressSize, 100);

    return {
        percent: `${finalPercent}%`,
        index: targetIndex,
    };
}

/**
 * 获取任务节点的位置列表
 * @param {HTMLElement} progressBarRef 容器DOM引用
 * @param {boolean} isVertical 是否垂直方向
 * @returns {Array} 位置列表
 */
function getPercentLocation(progressListRef, isVertical = false) {
    if (!progressListRef) {
        return [];
    }

    const children = progressListRef?.children;
    if (!children?.length) {
        return [];
    }

    const offsetKey = isVertical ? 'offsetTop' : 'offsetLeft';
    const sizeKey = isVertical ? 'clientHeight' : 'clientWidth';

    return Array.from(children).map((child) => {
        return child[offsetKey] + child[sizeKey] / 2;
    });
}

/**
 * 进度条计算Hook
 * @param {Ref|number} valueRef 当前值
 * @param {Ref|Array} taskConfigList 任务配置列表
 * @param {HTMLElement|Ref} progressListRef 进度条DOM引用
 * @param {HTMLElement|Ref} progressBarRef 容器DOM引用
 * @param {boolean} isVertical 是否垂直方向
 * @returns {object} 进度信息
 */
export default function useProgress(
    valueRef,
    taskConfigList,
    progressListRef,
    progressBarRef,
    isVertical = false,
) {
    const result = reactive({
        percent: '0%',
        index: 0,
    });

    watchEffect(() => {
        try {
            const wrappedValue = isRef(valueRef) ? valueRef.value : valueRef;
            const wrappedTaskConfigList = isRef(taskConfigList)
                ? taskConfigList.value
                : taskConfigList;
            const wrappedContainerRef = isRef(progressBarRef)
                ? progressBarRef.value
                : progressBarRef;
            const wrappedProgressListRef = isRef(progressListRef)
                ? progressListRef.value
                : progressListRef;

            if (!wrappedContainerRef || !wrappedProgressListRef) {
                return;
            }

            const percentLocationList = getPercentLocation(wrappedProgressListRef, isVertical);

            const progress = getProgress(
                wrappedValue,
                wrappedTaskConfigList,
                percentLocationList,
                wrappedProgressListRef,
                isVertical,
            );

            result.percent = progress.percent;
            result.index = progress.index;
        }
        catch (error) {
            console.error('进度计算出错:', error);
            result.percent = '0%';
            result.index = 0;
        }
    });

    return toRefs(result);
}

/**
 * 使用示例 Demo
 *
 * 在 Vue 组件中使用:
 *
 * <template>
 *   <div class="progress-demo">
 *     <div class="progress-bar" ref="progressBarRef">
 *       <div class="progress-track">
 *         <div
 *           class="progress-fill"
 *           :style="{ width: percent }"
 *         ></div>
 *       </div>
 *       <div class="progress-nodes" ref="progressListRef">
 *         <div
 *           v-for="(task, index) in taskList"
 *           :key="task.id"
 *           class="progress-node"
 *           :class="{ 'active': index <= currentIndex }"
 *         >
 *           {{ task.value }}
 *         </div>
 *       </div>
 *     </div>
 *
 *     <div class="controls">
 *       <button @click="updateValue(currentValue - 10)">-10</button>
 *       <span>当前值: {{ currentValue }}</span>
 *       <button @click="updateValue(currentValue + 10)">+10</button>
 *     </div>
 *   </div>
 * </template>
 *
 * <script setup>
 * import useProgress from '@/use/use-progress'
 *
 * // 定义任务配置
 * const taskList = ref([
 *   { id: 1, value: 50, title: '任务1' },
 *   { id: 2, value: 100, title: '任务2' },
 *   { id: 3, value: 200, title: '任务3' },
 *   { id: 4, value: 500, title: '任务4' },
 * ])
 *
 * // 当前进度值
 * const currentValue = ref(0)
 *
 * // DOM 引用
 * const progressBarRef = ref()
 * const progressListRef = ref()
 *
 * // 使用进度条 Hook
 * const { percent, index: currentIndex } = useProgress(
 *   currentValue,
 *   taskList,
 *   progressListRef,
 *   progressBarRef
 * )
 *
 * // 更新值的方法
 * function updateValue(newValue) {
 *   if (newValue >= 0 && newValue <= 500) {
 *     currentValue.value = newValue
 *   }
 * }
 *
 * // 也可以用于垂直进度条
 * const { percent: verticalPercent, index: verticalIndex } = useProgress(
 *   currentValue,
 *   taskList,
 *   progressListRef,
 *   progressBarRef,
 *   true // 垂直方向
 * )
 * </script>
 */
