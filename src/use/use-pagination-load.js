/**
 * 通用分页加载组合式函数
 * @param {object} options - 配置选项
 * @param {Function} options.api - API接口函数
 * @returns {object} 返回分页相关状态和方法
 */
export default function usePaginationLoad(options = {}) {
    const { api = null, params = {}, size = 10, listKey = 'list' } = options;

    // 使用reactive创建响应式状态对象
    const state = reactive({
        total: Number.MAX_VALUE,
        list: [],
        page: 1,
        loading: false,
        size,
    });

    /**
     * 获取分页数据
     * @param {object} extraParams - 可选的额外参数
     * @returns {Promise<{code: number}>} 请求结果
     */
    const loadMore = async (extraParams = {}) => {
        // 如果已经在加载，或者数据已全部加载完，或者没有提供API，则直接返回
        if (state.loading || state.list.length === state.total || !api) {
            return { code: -1, message: '重复请求或已加载全部数据' };
        }

        try {
            state.loading = true;

            const requestParams = {
                page: state.page,
                size: state.size,
                ...params,
                ...extraParams,
            };

            const [{ code, data }] = await api(requestParams);

            if (code === 0) {
                state.total = data.total;
                state.list = [...state.list, ...data[listKey]];
                state.page += 1;
            }

            return { code };
        }
        catch (error) {
            console.error('加载分页数据失败:', error);
            return { code: -1, error };
        }
        finally {
            state.loading = false;
        }
    };

    /**
     * 重置分页状态
     */
    function reset() {
        state.page = 1;
        state.total = Number.MAX_VALUE;
        state.list = [];
        state.loading = false;
    }

    return reactive({
        ...toRefs(state),
        loadMore,
        reset,
    });
}
