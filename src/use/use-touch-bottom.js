import { onUnmounted, watch } from 'vue';

const useTouchBottom = (domRef, callback) => {
    let dom;
    const onTouchBottom = () => {
        if (dom && dom.scrollTop + dom.clientHeight >= dom.scrollHeight - 30) {
            callback && callback();
        }
    };

    watch(domRef, () => {
        if (dom !== domRef.value && domRef.value) {
            dom = domRef.value;
            dom.addEventListener('scroll', onTouchBottom);
        }
    });

    onUnmounted(() => {
        dom && dom.removeEventListener('scroll', onTouchBottom);
    });
};

export default useTouchBottom;
