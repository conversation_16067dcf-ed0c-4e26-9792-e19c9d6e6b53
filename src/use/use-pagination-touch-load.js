import usePaginationLoad from './use-pagination-load';
import useTouchBottom from './use-touch-bottom';

/**
 * 集成分页加载和触底加载的组合式函数
 * @param {object} options - 配置选项
 * @param {Function} options.api - API接口函数
 * @param {number} options.size - 每页数据量
 * @param {object} options.params - 额外的API参数
 * @param {object} options.listRef - 列表元素的ref引用
 * @returns {object} 返回分页数据和控制方法
 */
export default function usePaginationTouchLoad(options = {}) {
    const { listRef, ...paginationOptions } = options;

    // 使用分页加载组合式函数
    const paginationStore = usePaginationLoad(paginationOptions);

    // 触底加载更多数据
    const onTouchBottom = () => {
        paginationStore.loadMore();
    };

    // 如果提供了列表引用，绑定触底事件
    if (listRef) {
        useTouchBottom(listRef, onTouchBottom);
    }

    return paginationStore;
}

/**
 * 使用示例 Demo
 *
 * 在 Vue 组件中使用:
 *
 * <template>
 *   <div class="list-container">
 *
 *     <!-- 列表容器 -->
 *     <div
 *       ref="listRef"
 *       class="scroll-list"
 *     >
 *       <!-- 数据列表 -->
 *       <div
 *         v-for="item in list"
 *         :key="item.id"
 *         class="list-item"
 *       >
 *       </div>
 *
 *     </div>
 *   </div>
 * </template>
 *
 * <script setup>
 * import { ref } from 'vue'
 * import usePaginationTouchLoad from '@/use/use-pagination-touch-load'
 * import { getProductList } from '@/api'
 *
 * // 列表容器引用
 * const listRef = ref()
 * // 使用分页触底加载
 * const {
 *   list,
 *   loadMore,
 *   reset,
 * } = usePaginationTouchLoad({
 *   api: getProductList,
 *   size: 20,
 *   listRef,
 *   params: computed(() => ({
 *     keyword: searchKeyword.value,
 *   })),
 * })
 *
 * </script>
 */
