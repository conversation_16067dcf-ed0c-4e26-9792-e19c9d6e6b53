/**
 * 增强版点击锁组合函数
 * @param {Function} fn - 需要包装的异步函数
 * @param {object} [options] - 配置选项
 * @param {boolean} [options.leading] - 是否立即执行首次点击
 * @param {Function} [options.onError] - 自定义错误处理函数
 * @param {Function} [options.onLocked] - 当被锁定时触发的回调函数
 * @returns {Function} 带锁功能的包装函数
 */
export function useClickLock(fn, options = {}) {
    const isLocked = ref(false);
    const { leading = true, onError, onLocked } = options;

    /**
     * 带锁功能的包装函数
     * @param {...*} args - 原函数的参数
     * @returns {Promise<*>|undefined} 原函数执行结果
     */
    return async function withLock(...args) {
        if (isLocked.value) {
            // 当被锁定时，执行自定义回调
            if (typeof onLocked === 'function') {
                onLocked(...args);
            }
            return;
        }

        try {
            leading && (isLocked.value = true);
            const result = await fn.apply(this, args);
            return result;
        }
        catch (err) {
            onError?.(err);
            console.error('ClickLock error:', err);
            throw err; // 保持错误冒泡
        }
        finally {
            isLocked.value = false;
        }
    };
}

export default useClickLock;
