import { fetchApi } from '.';
import { urlConfig } from '@/config/url-config';
import { env } from '@/config/url';

const osType = myWebview.isIOS() ? 'ios' : 'android';
// 实验层标签
export const GROUP_LAYER = {
    dev: 'iop_site_ad_expt_250327101429758',
    testing: 'iop_site_ad_expt_250327101429758',
    gray: 'iop_site_ad_expt_250401163309778',
    prod: 'iop_site_ad_expt_250401163309778',
};

// 实验组ID
export const GROUP_ID = {
    dev: '1503',
    testing: '1503',
    gray: '4680',
    prod: '4680',
};

export const groupInfo = (data, config = {}) =>
    fetchApi({
        proPrefix: 'abtest-group/activity.Activity',
        api: 'groupInfo',
        data,
        config: {
            ...config,
            baseURL: `https:${urlConfig[`node_common_${osType}`][env]}`,
        },
    });
