interface initReq {
    /**当前用户uid */
    uid: number;
    /**当前用户token */
    token: string;
}

interface initRes {
    /**服务器时间 */
    serverTime: number;
    /**活动开始时间 */
    startTime: number;
    /**活动结束时间 */
    endTime: number;
    /**用户信息 */
    userInfo: UserInfo;
    /**身份：0-无资格参与活动 1: 师傅 2: 徒弟 */
    identity: number;
    /**考核等级 S: 6, A: 5, B: 4, C: 3, D: 2, E: 1 */
    level: number;
    /**绑定信息 */
    bindInfo: UserInfo;
    /**队伍buff加成 20/10 */
    teamBuff: number;
    /**每日星光日榜团队赛道ID A B C D E --星光日榜根据此值所在赛道显示底部浮层 */
    dailyTeamRaceId: string;
}

interface UserInfo {
    /**用户uid */
    uid: number;
    /**用户ttid */
    alias: string;
    /**用户昵称 */
    nickname: string;
    /**用户头像 */
    username: string;
    /**用户性别 */
    sex: number;
}

interface UserInfo {
    /**用户uid */
    uid: number;
    /**用户ttid */
    alias: string;
    /**用户昵称 */
    nickname: string;
    /**用户头像 */
    username: string;
    /**用户性别 */
    sex: number;
}

interface inviteReq {
    /**当前用户uid */
    uid: number;
    /**当前用户token */
    token: string;
    /**被邀请人uid */
    inviteeUid: number;
}

interface emptyRes {}

interface acceptInviteReq {
    /**当前用户uid */
    uid: number;
    /**当前用户token */
    token: string;
    /**邀请人uid */
    inviterUid: number;
}

interface getInviteListReq {
    /**当前用户uid */
    uid: number;
    /**ttid搜索用 */
    ttid: number;
    /**页码 */
    page: number;
    /**每页数量 */
    size: number;
}

interface getInviteListRes {
    /**被邀请人列表 */
    list: itemInfo[];
    /**总数 */
    total: number;
}

interface itemInfo {
    /**用户信息 */
    userInfo: UserInfo;
    /**考核等级 S: 6, A: 5, B: 4, C: 3, D: 2, E: 1 */
    level: number;
    /**身份：0-无资格参与活动 1: 师傅 2: 徒弟 */
    identity: number;
    /**状态：0-未邀请 1-已发送邀请/已接受邀请 */
    acceptStatus: number;
}

interface UserInfo {
    /**用户uid */
    uid: number;
    /**用户ttid */
    alias: string;
    /**用户昵称 */
    nickname: string;
    /**用户头像 */
    username: string;
    /**用户性别 */
    sex: number;
}

interface getAcceptListReq {
    /**当前用户uid */
    uid: number;
    /**ttid搜索用 */
    ttid: number;
}

interface getTeamStarlightRecordReq {
    /**当前用户uid */
    uid: number;
    /**日期：格式：YYYYMMDD */
    date: number;
}

interface getTeamStarlightRecordRes {
    /**星光值记录-按需求文档顺序返回 */
    list: StarlightRecord[];
    /**当前任务完成获得的总星光值 */
    totalValue: number;
}

interface StarlightRecord {
    /**任务id-按需求文档顺序返回 live1hour add1Fans add1PayUser pk1Link mic10min receiveDecreaseGift receive1Gift */
    id: number;
    /**每次完成任务获得的星光值 */
    value: number;
    /**限制完成次数 */
    limit: number;
    /**完成进度：单位：秒数/个数 */
    completedCount: number;
    /**任务完成获得的星光值 */
    curValue: number;
}

interface getMyStarlightRes {
    /**我的星光值 */
    myStarlight: number;
    /**我的奖励状态 0-未获得 1-未领取 2-已领取 */
    rewardStatus: number;
}

interface getDailyLuckyList {
    /**每日幸运儿列表 */
    list: UserInfo[];
}

interface UserInfo {
    /**用户uid */
    uid: number;
    /**用户ttid */
    alias: string;
    /**用户昵称 */
    nickname: string;
    /**用户头像 */
    username: string;
    /**用户性别 */
    sex: number;
}

interface getStarlightRankReq {
    page: number;
    size: number;
    /**当前操作用户uid */
    uid: number;
    /**榜单日期 YYYYMMDD 20230501 不传默认为总榜 */
    date: string;
    /**日榜必传：每日星光日榜团队赛道ID A B C D E --星光日榜根据此值所在赛道显示底部浮层 */
    dailyTeamRaceId: string;
}

interface getStarlightRankRes {
    /**总数 */
    total: number;
    /**列表信息 */
    list: Item[];
    /**底部我的排名信息 */
    self: myRankInfo;
}

interface Item {
    /**榜单排名 */
    rank: string;
    /**榜单值 */
    value: number;
    /**师傅信息 */
    userInfo: UserInfo;
    /**房间信息 */
    channelInfo: ChannelInfo;
    /**徒弟信息 */
    discipleInfo: DiscipleInfo;
}

interface UserInfo {
    /**用户uid */
    uid: number;
    /**用户ttid */
    alias: string;
    /**用户昵称 */
    nickname: string;
    /**用户头像 */
    username: string;
    /**用户性别 */
    sex: number;
}

interface ChannelInfo {
    /**房间id */
    channelId: number;
    /**房间状态 0:不在房间 1:房间中 2:看直播 3:直播中 4:PK中 */
    status: number;
}

interface DiscipleInfo {
    /**徒弟信息 */
    userInfo: UserInfo;
    /**房间信息 */
    channelInfo: ChannelInfo;
    /**徒弟考核等级 S: 6, A: 5, B: 4, C: 3, D: 2, E: 1 */
    level: number;
}

interface UserInfo {
    /**用户uid */
    uid: number;
    /**用户ttid */
    alias: string;
    /**用户昵称 */
    nickname: string;
    /**用户头像 */
    username: string;
    /**用户性别 */
    sex: number;
}

interface ChannelInfo {
    /**房间id */
    channelId: number;
    /**房间状态 0:不在房间 1:房间中 2:看直播 3:直播中 4:PK中 */
    status: number;
}

interface myRankInfo {
    /**师傅信息 */
    userInfo: UserInfo;
    /**榜单排名 */
    rank: string;
    /**榜单值 */
    value: number;
    /**距上一名描述 */
    prevDescribe: string;
    /**超下一名描述 */
    nextDescribe: string;
    /**徒弟信息 */
    discipleInfo: DiscipleInfo;
}

interface UserInfo {
    /**用户uid */
    uid: number;
    /**用户ttid */
    alias: string;
    /**用户昵称 */
    nickname: string;
    /**用户头像 */
    username: string;
    /**用户性别 */
    sex: number;
}

interface DiscipleInfo {
    /**徒弟信息 */
    userInfo: UserInfo;
    /**房间信息 */
    channelInfo: ChannelInfo;
    /**徒弟考核等级 S: 6, A: 5, B: 4, C: 3, D: 2, E: 1 */
    level: number;
}

// 官频巡演相关接口类型定义
interface GetTourListReq {
    uid?: number;
}

interface TourAnchor {
    /**主播ID */
    anchorId: string;
    /**主播昵称 */
    nickname: string;
    /**主播头像 */
    avatar: string;
    /**是否在线 */
    isOnline: boolean;
    /**是否已关注 */
    isFollowed: boolean;
    /**直播间ID */
    roomId: string;
    /**表演开始时间 */
    startTime: number;
    /**表演结束时间 */
    endTime: number;
    /**时间段描述 */
    timeSlot: string;
}

interface TourSchedule {
    /**场次ID */
    scheduleId: string;
    /**场次名称 */
    scheduleName: string;
    /**场次开始时间 */
    startTime: number;
    /**场次结束时间 */
    endTime: number;
    /**场次状态: 0-未开始, 1-进行中, 2-已结束 */
    status: number;
    /**主播列表 */
    anchors: TourAnchor[];
}

interface GetTourListResp {
    /**巡演场次列表 */
    schedules: TourSchedule[];
    /**当前场次ID */
    currentScheduleId?: string;
    /**当前表演主播ID */
    currentAnchorId?: string;
}

interface FollowAnchorReq {
    uid: number;
    anchorId: string;
}

interface FollowAnchorResp {
    success: boolean;
}

interface UnfollowAnchorReq {
    uid: number;
    anchorId: string;
}

interface UnfollowAnchorResp {
    success: boolean;
}

export type none = {};
